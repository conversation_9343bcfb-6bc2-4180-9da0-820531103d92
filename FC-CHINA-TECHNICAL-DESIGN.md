# FC-CHINA Technical Design Document

## 📋 **Document Information**
- **Project**: FC-CHINA Multi-Tenant Factory Admin Platform
- **Version**: 1.0
- **Last Updated**: December 2024
- **Document Type**: Technical Design Document
- **Status**: Draft

---

## 🏗️ **System Architecture Overview**

**Platform Scope**: FC-CHINA is designed for:
- ✅ **Web Browser Application**: Responsive design for desktop and mobile browsers
- ✅ **Mobile Applications**: Native Flutter apps for Android and iOS smartphones/tablets
- ❌ **Desktop Applications**: No native desktop apps (Windows/macOS/Linux)

### **High-Level Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │ Mobile Apps     │    │  Admin Portal   │
│   (Next.js 15)  │    │ (Flutter)       │    │   (Next.js 15)  │
│   Responsive    │    │ Android & iOS   │    │   Web Browser   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      Load Balancer        │
                    │      (Nginx/Cloudflare)   │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     API Gateway           │
                    │   (Express + tRPC)        │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                       │                        │
┌───────┴───────┐    ┌─────────┴─────────┐    ┌─────────┴─────────┐
│   Auth Service │    │  Business Logic   │    │   File Storage    │
│    (Auth0)     │    │   (Node.js +      │    │   (Supabase)      │
│                │    │    Prisma)        │    │                   │
└───────┬───────┘    └─────────┬─────────┘    └───────────────────┘
        │                      │
        │              ┌───────┴───────┐
        │              │   Database    │
        │              │  (Supabase    │
        │              │  PostgreSQL)  │
        │              └───────────────┘
        │
┌───────┴───────┐    ┌─────────────────┐    ┌─────────────────┐
│  Real-time     │    │    Caching      │    │   Monitoring    │
│  (Socket.io +  │    │    (Redis)      │    │  (DataDog/      │
│   Supabase)    │    │                 │    │   Sentry)       │
└───────────────┘    └─────────────────┘    └─────────────────┘
```

### **Technology Stack**

#### **Frontend**
- **Web**: Next.js 15 + TypeScript + Tailwind CSS + shadcn/ui
- **Mobile**: Flutter 3.x + Dart + Riverpod
- **State Management**: TanStack Query (React), Riverpod (Flutter)
- **UI Components**: shadcn/ui (Web), Custom Flutter widgets

#### **Backend**
- **Runtime**: Node.js 20+
- **Framework**: Express.js
- **API Layer**: tRPC v10
- **ORM**: Prisma 5+
- **Validation**: Zod
- **Authentication**: Auth0

#### **Database & Storage**
- **Primary Database**: Supabase (PostgreSQL 15+)
- **File Storage**: Supabase Storage
- **Caching**: Redis (for sessions and frequently accessed data)
- **Search**: PostgreSQL Full-Text Search + pg_trgm

#### **Infrastructure**
- **Deployment**: Vercel (Frontend), Railway/Render (Backend)
- **CDN**: Cloudflare
- **Monitoring**: Sentry (Error tracking), DataDog (Performance)
- **CI/CD**: GitHub Actions

#### **Environment Configuration**
- **Development**: Local Docker Compose setup
- **Staging**: Vercel Preview + Railway staging environment
- **Production**: Vercel Production + Railway production environment
- **Environment Variables**: Managed via platform-specific dashboards
- **Secrets Management**: GitHub Secrets for CI/CD, platform environment variables for runtime

---

## 🗄️ **Database Schema Design**

### **Core Entities & Relationships**

```prisma
// schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// CORE ENTITIES
// ============================================================================

model Factory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique // for subdomain
  description String?
  logo        String?
  website     String?
  
  // Contact Information
  email       String
  phone       String?
  address     Json     // {street, city, state, country, zipCode}
  
  // Business Information
  businessLicense String?
  industry        String
  establishedYear Int?
  employeeCount   Int?
  
  // Platform Settings
  status          FactoryStatus @default(PENDING)
  subscriptionTier SubscriptionTier @default(TRIAL)
  settings        Json          // factory-specific settings
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  users       User[]
  products    Product[]
  categories  Category[]
  orders      Order[]
  messages    Message[]
  quotes      Quote[]
  
  @@map("factories")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  avatar    String?
  phone     String?
  
  // Authentication
  auth0Id   String   @unique
  
  // Role & Permissions
  role      UserRole
  
  // Factory Association (null for customers and platform admins)
  factoryId String?
  factory   Factory? @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Customer-specific fields
  company   String?  // for customer users
  
  // Preferences
  language  String   @default("en")
  timezone  String   @default("UTC")
  settings  Json     @default("{}")
  
  // Status
  status    UserStatus @default(ACTIVE)
  lastLoginAt DateTime?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  sentMessages     Message[] @relation("MessageSender")
  receivedMessages Message[] @relation("MessageReceiver")
  orders           Order[]
  quotes           Quote[]
  auditLogs        AuditLog[]
  
  @@map("users")
}

model Category {
  id          String    @id @default(cuid())
  name        String
  slug        String
  description String?
  image       String?
  
  // Hierarchy
  parentId    String?
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  
  // Factory Association
  factoryId   String
  factory     Factory   @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Metadata
  sortOrder   Int       @default(0)
  isActive    Boolean   @default(true)
  
  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // Relationships
  products    Product[]
  
  @@unique([factoryId, slug])
  @@map("categories")
}

model Product {
  id          String   @id @default(cuid())
  name        String
  slug        String
  description String?
  
  // Product Details
  sku         String?
  specifications Json   // flexible product specs
  
  // Pricing
  basePrice   Decimal  @db.Decimal(10, 2)
  currency    String   @default("USD")
  
  // Inventory
  stockQuantity Int    @default(0)
  minOrderQty   Int    @default(1)
  maxOrderQty   Int?
  
  // Media
  images      String[] // array of image URLs
  videos      String[] // array of video URLs
  documents   String[] // array of document URLs
  
  // SEO & Marketing
  metaTitle       String?
  metaDescription String?
  tags            String[]
  
  // Status
  status      ProductStatus @default(DRAFT)
  isActive    Boolean       @default(true)
  isFeatured  Boolean       @default(false)
  
  // Factory Association
  factoryId   String
  factory     Factory   @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Category Association
  categoryId  String
  category    Category  @relation(fields: [categoryId], references: [id])
  
  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // Relationships
  variants    ProductVariant[]
  orderItems  OrderItem[]
  quoteItems  QuoteItem[]
  messages    Message[]
  
  @@unique([factoryId, slug])
  @@map("products")
}

model ProductVariant {
  id        String  @id @default(cuid())
  name      String  // e.g., "Red - Large"
  sku       String?
  
  // Variant Attributes
  attributes Json   // {color: "red", size: "large"}
  
  // Pricing Override
  price     Decimal? @db.Decimal(10, 2)
  
  // Inventory
  stockQuantity Int @default(0)
  
  // Media
  images    String[]
  
  // Status
  isActive  Boolean @default(true)
  
  // Product Association
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("product_variants")
}

// ============================================================================
// COMMUNICATION & MESSAGING
// ============================================================================

model Message {
  id        String      @id @default(cuid())
  content   String
  type      MessageType @default(TEXT)
  
  // Attachments
  attachments Json @default("[]")
  
  // Participants
  senderId    String
  sender      User   @relation("MessageSender", fields: [senderId], references: [id])
  
  receiverId  String
  receiver    User   @relation("MessageReceiver", fields: [receiverId], references: [id])
  
  // Context
  productId   String?
  product     Product? @relation(fields: [productId], references: [id])
  
  orderId     String?
  order       Order?   @relation(fields: [orderId], references: [id])
  
  // Status
  isRead      Boolean  @default(false)
  readAt      DateTime?
  
  // Factory Context
  factoryId   String
  factory     Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("messages")
}

// ============================================================================
// QUOTES & ORDERS
// ============================================================================

model Quote {
  id          String      @id @default(cuid())
  quoteNumber String      @unique
  
  // Customer
  customerId  String
  customer    User        @relation(fields: [customerId], references: [id])
  
  // Factory
  factoryId   String
  factory     Factory     @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Quote Details
  title       String?
  notes       String?
  
  // Pricing
  subtotal    Decimal     @db.Decimal(10, 2)
  taxAmount   Decimal     @db.Decimal(10, 2) @default(0)
  shippingCost Decimal    @db.Decimal(10, 2) @default(0)
  totalAmount Decimal     @db.Decimal(10, 2)
  currency    String      @default("USD")
  
  // Terms
  paymentTerms String?
  deliveryTerms String?
  validUntil   DateTime
  
  // Status
  status      QuoteStatus @default(DRAFT)
  
  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // Relationships
  items       QuoteItem[]
  order       Order?      // one-to-one if quote is accepted
  
  @@map("quotes")
}

model QuoteItem {
  id        String  @id @default(cuid())
  
  // Product Reference
  productId String
  product   Product @relation(fields: [productId], references: [id])
  
  // Quote Reference
  quoteId   String
  quote     Quote   @relation(fields: [quoteId], references: [id], onDelete: Cascade)
  
  // Item Details
  quantity  Int
  unitPrice Decimal @db.Decimal(10, 2)
  totalPrice Decimal @db.Decimal(10, 2)
  
  // Specifications
  specifications Json @default("{}")
  notes         String?
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("quote_items")
}

model Order {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  
  // Customer
  customerId  String
  customer    User        @relation(fields: [customerId], references: [id])
  
  // Factory
  factoryId   String
  factory     Factory     @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Quote Reference (if order came from quote)
  quoteId     String?     @unique
  quote       Quote?      @relation(fields: [quoteId], references: [id])
  
  // Order Details
  notes       String?
  
  // Pricing
  subtotal    Decimal     @db.Decimal(10, 2)
  taxAmount   Decimal     @db.Decimal(10, 2) @default(0)
  shippingCost Decimal    @db.Decimal(10, 2) @default(0)
  totalAmount Decimal     @db.Decimal(10, 2)
  currency    String      @default("USD")
  
  // Shipping
  shippingAddress Json
  shippingMethod  String?
  trackingNumber  String?
  
  // Status & Timeline
  status          OrderStatus @default(PENDING)
  estimatedDelivery DateTime?
  actualDelivery    DateTime?
  
  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // Relationships
  items       OrderItem[]
  messages    Message[]
  statusHistory OrderStatusHistory[]
  
  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  
  // Product Reference
  productId String
  product   Product @relation(fields: [productId], references: [id])
  
  // Order Reference
  orderId   String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  // Item Details
  quantity  Int
  unitPrice Decimal @db.Decimal(10, 2)
  totalPrice Decimal @db.Decimal(10, 2)
  
  // Specifications
  specifications Json @default("{}")
  notes         String?
  
  // Production Status
  productionStatus ProductionStatus @default(PENDING)
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@map("order_items")
}

model OrderStatusHistory {
  id        String      @id @default(cuid())
  
  // Order Reference
  orderId   String
  order     Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  // Status Change
  fromStatus OrderStatus?
  toStatus   OrderStatus
  notes      String?
  
  // Metadata
  changedBy  String?     // user ID who made the change
  
  // Timestamp
  createdAt  DateTime    @default(now())
  
  @@map("order_status_history")
}

// ============================================================================
// AUDIT & LOGGING
// ============================================================================

model AuditLog {
  id        String   @id @default(cuid())
  
  // Action Details
  action    String   // e.g., "CREATE_PRODUCT", "UPDATE_ORDER"
  entity    String   // e.g., "Product", "Order"
  entityId  String   // ID of the affected entity
  
  // User Context
  userId    String?
  user      User?    @relation(fields: [userId], references: [id])
  
  // Factory Context
  factoryId String?
  
  // Change Details
  oldValues Json?    // previous state
  newValues Json?    // new state
  metadata  Json     @default("{}")
  
  // Request Context
  ipAddress String?
  userAgent String?
  
  // Timestamp
  createdAt DateTime @default(now())
  
  @@map("audit_logs")
}

// ============================================================================
// ENUMS
// ============================================================================

enum UserRole {
  PLATFORM_ADMIN
  FACTORY_ADMIN
  FACTORY_STAFF
  CUSTOMER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

enum FactoryStatus {
  PENDING
  APPROVED
  SUSPENDED
  REJECTED
}

enum SubscriptionTier {
  TRIAL
  BASIC
  PREMIUM
  ENTERPRISE
}

enum ProductStatus {
  DRAFT
  PENDING_APPROVAL
  ACTIVE
  INACTIVE
  DISCONTINUED
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  SYSTEM
}

enum QuoteStatus {
  DRAFT
  SENT
  VIEWED
  ACCEPTED
  REJECTED
  EXPIRED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  IN_PRODUCTION
  QUALITY_CHECK
  SHIPPED
  DELIVERED
  COMPLETED
  CANCELLED
  ON_HOLD
  RETURNED
}

enum ProductionStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  ON_HOLD
}
```

### **Database Indexes & Performance**

```sql
-- Performance Indexes
CREATE INDEX idx_products_factory_status ON products(factory_id, status);
CREATE INDEX idx_products_category_active ON products(category_id, is_active);
CREATE INDEX idx_orders_factory_status ON orders(factory_id, status);
CREATE INDEX idx_orders_customer_created ON orders(customer_id, created_at DESC);
CREATE INDEX idx_messages_factory_created ON messages(factory_id, created_at DESC);
CREATE INDEX idx_messages_participants ON messages(sender_id, receiver_id);
CREATE INDEX idx_audit_logs_factory_created ON audit_logs(factory_id, created_at DESC);

-- Full-text search indexes
CREATE INDEX idx_products_search ON products USING gin(to_tsvector('english', name || ' ' || description));
CREATE INDEX idx_factories_search ON factories USING gin(to_tsvector('english', name || ' ' || description));

-- Partial indexes for active records
CREATE INDEX idx_products_active ON products(factory_id, created_at DESC) WHERE is_active = true;
CREATE INDEX idx_users_active ON users(factory_id) WHERE status = 'ACTIVE';

-- Database Connection Management
-- Connection pooling configuration for production
-- Max connections: 100 (adjust based on load)
-- Connection timeout: 30 seconds
-- Idle timeout: 10 minutes
-- Pool size per instance: 10-20 connections
```

### **Database Migration Strategy**

```typescript
// prisma/migrations/migration-utils.ts
export class MigrationManager {
  static async runMigration(migrationName: string) {
    console.log(`Starting migration: ${migrationName}`);

    // Create backup before migration
    await this.createBackup();

    try {
      // Run Prisma migration
      await prisma.$executeRaw`BEGIN;`;

      // Custom migration logic here
      await this.executeMigrationSteps();

      await prisma.$executeRaw`COMMIT;`;

      console.log(`Migration ${migrationName} completed successfully`);
    } catch (error) {
      await prisma.$executeRaw`ROLLBACK;`;
      console.error(`Migration ${migrationName} failed:`, error);

      // Restore from backup if needed
      await this.restoreFromBackup();
      throw error;
    }
  }

  static async createBackup() {
    // Implementation for database backup
    const timestamp = new Date().toISOString();
    console.log(`Creating backup at ${timestamp}`);
  }

  static async executeMigrationSteps() {
    // Custom migration steps that can't be handled by Prisma
  }

  static async restoreFromBackup() {
    // Implementation for backup restoration
  }
}
```

---

## 🔌 **API Design & tRPC Procedures**

### **API Structure**

```typescript
// src/routers/index.ts
export const appRouter = router({
  auth: authRouter,
  factories: factoriesRouter,
  users: usersRouter,
  products: productsRouter,
  categories: categoriesRouter,
  orders: ordersRouter,
  quotes: quotesRouter,
  messages: messagesRouter,
  uploads: uploadsRouter,
  analytics: analyticsRouter,
  search: searchRouter,
});

export type AppRouter = typeof appRouter;

### **API Versioning Strategy**

```typescript
// src/lib/versioning.ts
export const API_VERSIONS = {
  V1: 'v1',
  V2: 'v2'
} as const;

export type ApiVersion = typeof API_VERSIONS[keyof typeof API_VERSIONS];

// Version-aware router
export const createVersionedRouter = (version: ApiVersion) => {
  return router({
    // Version-specific implementations
    products: version === 'v1' ? productsRouterV1 : productsRouterV2,
    orders: version === 'v1' ? ordersRouterV1 : ordersRouterV2,
    // ... other routers
  });
};

// Main versioned app router
export const versionedAppRouter = router({
  v1: createVersionedRouter('v1'),
  v2: createVersionedRouter('v2'),
  // Current version (alias to latest)
  current: createVersionedRouter('v2')
});

// Middleware for version detection
export const versionMiddleware = t.middleware(async ({ ctx, next }) => {
  const version = ctx.req.headers['api-version'] || 'v2';

  if (!Object.values(API_VERSIONS).includes(version as ApiVersion)) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `Unsupported API version: ${version}`
    });
  }

  return next({
    ctx: {
      ...ctx,
      apiVersion: version as ApiVersion
    }
  });
});
```

### **Global Error Handling**

```typescript
// src/lib/error-handler.ts
import { TRPCError } from '@trpc/server';
import { logger } from './logger';

export class GlobalErrorHandler {
  static handleError(error: unknown, context: {
    userId?: string;
    factoryId?: string;
    procedure: string;
    input?: any;
  }) {
    const errorId = generateErrorId();

    // Log error with context
    logger.error('API Error', {
      errorId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      ...context,
      timestamp: new Date().toISOString()
    });

    // Convert to appropriate TRPC error
    if (error instanceof TRPCError) {
      return error;
    }

    if (error instanceof PrismaClientKnownRequestError) {
      return this.handlePrismaError(error, errorId);
    }

    if (error instanceof ValidationError) {
      return new TRPCError({
        code: 'BAD_REQUEST',
        message: error.message,
        cause: { errorId }
      });
    }

    // Generic server error
    return new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred',
      cause: { errorId }
    });
  }

  static handlePrismaError(error: PrismaClientKnownRequestError, errorId: string): TRPCError {
    switch (error.code) {
      case 'P2002':
        return new TRPCError({
          code: 'CONFLICT',
          message: 'A record with this information already exists',
          cause: { errorId, field: error.meta?.target }
        });
      case 'P2025':
        return new TRPCError({
          code: 'NOT_FOUND',
          message: 'Record not found',
          cause: { errorId }
        });
      case 'P2003':
        return new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Foreign key constraint failed',
          cause: { errorId }
        });
      default:
        return new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Database operation failed',
          cause: { errorId }
        });
    }
  }
}

// Error handling middleware
export const errorHandlingMiddleware = t.middleware(async ({ ctx, next, path, input }) => {
  try {
    return await next();
  } catch (error) {
    throw GlobalErrorHandler.handleError(error, {
      userId: ctx.user?.id,
      factoryId: ctx.user?.factoryId,
      procedure: path,
      input: sanitizeInput(input)
    });
  }
});

function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substring(7)}`;
}

function sanitizeInput(input: any): any {
  // Remove sensitive data from input for logging
  if (!input) return input;

  const sanitized = { ...input };
  const sensitiveFields = ['password', 'token', 'secret', 'key'];

  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}
```
```

### **Authentication & Authorization**

```typescript
// src/lib/auth.ts
export const authMiddleware = t.middleware(async ({ ctx, next }) => {
  const token = ctx.req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  
  try {
    const decoded = await verifyAuth0Token(token);
    const user = await ctx.db.user.findUnique({
      where: { auth0Id: decoded.sub },
      include: { factory: true }
    });
    
    if (!user) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
    
    return next({
      ctx: {
        ...ctx,
        user,
        factory: user.factory,
      }
    });
  } catch (error) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
});

// Role-based middleware
export const requireFactoryAdmin = authMiddleware.unstable_pipe(
  async ({ ctx, next }) => {
    if (ctx.user.role !== 'FACTORY_ADMIN') {
      throw new TRPCError({ code: 'FORBIDDEN' });
    }
    return next({ ctx });
  }
);

export const requireFactoryAccess = authMiddleware.unstable_pipe(
  async ({ ctx, next }) => {
    if (!ctx.user.factoryId) {
      throw new TRPCError({ code: 'FORBIDDEN' });
    }
    return next({ ctx });
  }
);
```

### **Core API Procedures**

#### **Product Management**

```typescript
// src/routers/products.ts
export const productsRouter = router({
  // Get products with filtering and pagination
  getProducts: publicProcedure
    .input(z.object({
      factoryId: z.string(),
      categoryId: z.string().optional(),
      search: z.string().optional(),
      status: z.nativeEnum(ProductStatus).optional(),
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      sortBy: z.enum(['name', 'price', 'createdAt']).default('createdAt'),
      sortOrder: z.enum(['asc', 'desc']).default('desc'),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, factoryId, categoryId, search, status, sortBy, sortOrder } = input;
      const skip = (page - 1) * limit;
      
      const where: Prisma.ProductWhereInput = {
        factoryId,
        isActive: true,
        ...(categoryId && { categoryId }),
        ...(status && { status }),
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
            { tags: { has: search } },
          ]
        })
      };
      
      const [products, total] = await Promise.all([
        ctx.db.product.findMany({
          where,
          include: {
            category: true,
            variants: true,
            _count: { select: { orderItems: true } }
          },
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder }
        }),
        ctx.db.product.count({ where })
      ]);
      
      return {
        products,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    }),
    
  // Create product (factory staff only)
  createProduct: requireFactoryAccess
    .input(z.object({
      name: z.string().min(1).max(200),
      description: z.string().optional(),
      categoryId: z.string(),
      basePrice: z.number().positive(),
      currency: z.string().default('USD'),
      stockQuantity: z.number().min(0).default(0),
      minOrderQty: z.number().min(1).default(1),
      maxOrderQty: z.number().positive().optional(),
      specifications: z.record(z.any()).default({}),
      images: z.array(z.string().url()).default([]),
      tags: z.array(z.string()).default([]),
    }))
    .mutation(async ({ input, ctx }) => {
      // Validate category belongs to factory
      const category = await ctx.db.category.findFirst({
        where: {
          id: input.categoryId,
          factoryId: ctx.user.factoryId!
        }
      });
      
      if (!category) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invalid category'
        });
      }
      
      // Generate slug
      const slug = generateSlug(input.name);
      
      const product = await ctx.db.product.create({
        data: {
          ...input,
          slug,
          factoryId: ctx.user.factoryId!,
          status: ctx.user.role === 'FACTORY_ADMIN' ? 'ACTIVE' : 'PENDING_APPROVAL'
        },
        include: {
          category: true,
          factory: true
        }
      });
      
      // Log audit trail
      await logAuditEvent({
        action: 'CREATE_PRODUCT',
        entity: 'Product',
        entityId: product.id,
        userId: ctx.user.id,
        factoryId: ctx.user.factoryId!,
        newValues: product
      });
      
      return product;
    }),
    
  // Update product
  updateProduct: requireFactoryAccess
    .input(z.object({
      id: z.string(),
      name: z.string().min(1).max(200).optional(),
      description: z.string().optional(),
      categoryId: z.string().optional(),
      basePrice: z.number().positive().optional(),
      stockQuantity: z.number().min(0).optional(),
      specifications: z.record(z.any()).optional(),
      images: z.array(z.string().url()).optional(),
      tags: z.array(z.string()).optional(),
      status: z.nativeEnum(ProductStatus).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { id, ...updateData } = input;
      
      // Verify product belongs to factory
      const existingProduct = await ctx.db.product.findFirst({
        where: {
          id,
          factoryId: ctx.user.factoryId!
        }
      });
      
      if (!existingProduct) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found'
        });
      }
      
      // Check permissions for status changes
      if (updateData.status && ctx.user.role !== 'FACTORY_ADMIN') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only factory admins can change product status'
        });
      }
      
      const updatedProduct = await ctx.db.product.update({
        where: { id },
        data: updateData,
        include: {
          category: true,
          factory: true
        }
      });
      
      // Log audit trail
      await logAuditEvent({
        action: 'UPDATE_PRODUCT',
        entity: 'Product',
        entityId: id,
        userId: ctx.user.id,
        factoryId: ctx.user.factoryId!,
        oldValues: existingProduct,
        newValues: updatedProduct
      });
      
      return updatedProduct;
    }),
});
```

#### **Order Management**

```typescript
// src/routers/orders.ts
export const ordersRouter = router({
  // Create order from quote or direct
  createOrder: authMiddleware
    .input(z.object({
      factoryId: z.string(),
      quoteId: z.string().optional(),
      items: z.array(z.object({
        productId: z.string(),
        quantity: z.number().min(1),
        specifications: z.record(z.any()).default({}),
        notes: z.string().optional()
      })),
      shippingAddress: z.object({
        street: z.string(),
        city: z.string(),
        state: z.string(),
        country: z.string(),
        zipCode: z.string(),
        contactName: z.string(),
        contactPhone: z.string()
      }),
      notes: z.string().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const { factoryId, quoteId, items, shippingAddress, notes } = input;
      
      // Validate factory exists
      const factory = await ctx.db.factory.findUnique({
        where: { id: factoryId }
      });
      
      if (!factory) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Factory not found'
        });
      }
      
      // If creating from quote, validate quote
      let quote = null;
      if (quoteId) {
        quote = await ctx.db.quote.findFirst({
          where: {
            id: quoteId,
            customerId: ctx.user.id,
            factoryId,
            status: 'ACCEPTED'
          },
          include: { items: true }
        });
        
        if (!quote) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid or unaccepted quote'
          });
        }
      }
      
      // Calculate pricing
      let subtotal = new Decimal(0);
      const orderItems = [];
      
      for (const item of items) {
        const product = await ctx.db.product.findFirst({
          where: {
            id: item.productId,
            factoryId,
            status: 'ACTIVE',
            isActive: true
          }
        });
        
        if (!product) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: `Product ${item.productId} not available`
          });
        }
        
        // Check minimum order quantity
        if (item.quantity < product.minOrderQty) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: `Minimum order quantity for ${product.name} is ${product.minOrderQty}`
          });
        }
        
        const itemTotal = product.basePrice.mul(item.quantity);
        subtotal = subtotal.add(itemTotal);
        
        orderItems.push({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: product.basePrice,
          totalPrice: itemTotal,
          specifications: item.specifications,
          notes: item.notes
        });
      }
      
      // Generate order number
      const orderNumber = await generateOrderNumber(factoryId);
      
      // Create order in transaction
      const order = await ctx.db.$transaction(async (tx) => {
        const newOrder = await tx.order.create({
          data: {
            orderNumber,
            customerId: ctx.user.id,
            factoryId,
            quoteId,
            subtotal,
            totalAmount: subtotal, // TODO: Add tax and shipping calculation
            shippingAddress,
            notes,
            items: {
              create: orderItems
            }
          },
          include: {
            items: {
              include: { product: true }
            },
            customer: true,
            factory: true
          }
        });
        
        // Create initial status history
        await tx.orderStatusHistory.create({
          data: {
            orderId: newOrder.id,
            toStatus: 'PENDING',
            notes: 'Order created'
          }
        });
        
        // Update product inventory
        for (const item of orderItems) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stockQuantity: {
                decrement: item.quantity
              }
            }
          });
        }
        
        return newOrder;
      });
      
      // Send notifications
      await sendOrderNotification(order);
      
      return order;
    }),
    
  // Update order status (factory only)
  updateOrderStatus: requireFactoryAccess
    .input(z.object({
      orderId: z.string(),
      status: z.nativeEnum(OrderStatus),
      notes: z.string().optional(),
      trackingNumber: z.string().optional(),
      estimatedDelivery: z.date().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const { orderId, status, notes, trackingNumber, estimatedDelivery } = input;
      
      // Verify order belongs to factory
      const existingOrder = await ctx.db.order.findFirst({
        where: {
          id: orderId,
          factoryId: ctx.user.factoryId!
        }
      });
      
      if (!existingOrder) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Order not found'
        });
      }
      
      // Validate status transition
      if (!isValidStatusTransition(existingOrder.status, status)) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `Cannot change status from ${existingOrder.status} to ${status}`
        });
      }
      
      const updatedOrder = await ctx.db.$transaction(async (tx) => {
        // Update order
        const order = await tx.order.update({
          where: { id: orderId },
          data: {
            status,
            ...(trackingNumber && { trackingNumber }),
            ...(estimatedDelivery && { estimatedDelivery }),
            ...(status === 'DELIVERED' && { actualDelivery: new Date() })
          },
          include: {
            items: { include: { product: true } },
            customer: true,
            factory: true
          }
        });
        
        // Create status history
        await tx.orderStatusHistory.create({
          data: {
            orderId,
            fromStatus: existingOrder.status,
            toStatus: status,
            notes,
            changedBy: ctx.user.id
          }
        });
        
        return order;
      });
      
      // Send notifications
      await sendOrderStatusNotification(updatedOrder);
      
      return updatedOrder;
    })
});
```

#### **File Upload Implementation**

```typescript
// src/routers/uploads.ts
import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';
import { requireFactoryAccess } from '../lib/auth';
import { router } from '../lib/trpc';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_KEY!
);

export const uploadsRouter = router({
  // Generate signed upload URL
  generateUploadUrl: requireFactoryAccess
    .input(z.object({
      fileName: z.string(),
      fileType: z.string(),
      fileSize: z.number().max(10 * 1024 * 1024), // 10MB limit
      uploadType: z.enum(['product-image', 'document', 'avatar', 'factory-logo'])
    }))
    .mutation(async ({ input, ctx }) => {
      const { fileName, fileType, fileSize, uploadType } = input;

      // Validate file type
      const allowedTypes = {
        'product-image': ['image/jpeg', 'image/png', 'image/webp'],
        'document': ['application/pdf', 'application/msword', 'text/plain'],
        'avatar': ['image/jpeg', 'image/png'],
        'factory-logo': ['image/jpeg', 'image/png', 'image/svg+xml']
      };

      if (!allowedTypes[uploadType].includes(fileType)) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `File type ${fileType} not allowed for ${uploadType}`
        });
      }

      // Generate unique file path
      const fileExtension = fileName.split('.').pop();
      const uniqueFileName = `${ctx.user.factoryId}/${uploadType}/${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExtension}`;

      // Generate signed URL
      const { data, error } = await supabase.storage
        .from('fc-china-uploads')
        .createSignedUploadUrl(uniqueFileName);

      if (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate upload URL'
        });
      }

      return {
        uploadUrl: data.signedUrl,
        filePath: uniqueFileName,
        publicUrl: `${process.env.SUPABASE_URL}/storage/v1/object/public/fc-china-uploads/${uniqueFileName}`
      };
    }),

  // Confirm upload and process file
  confirmUpload: requireFactoryAccess
    .input(z.object({
      filePath: z.string(),
      uploadType: z.enum(['product-image', 'document', 'avatar', 'factory-logo']),
      metadata: z.object({
        originalName: z.string(),
        size: z.number(),
        mimeType: z.string()
      }).optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const { filePath, uploadType, metadata } = input;

      // Verify file exists in storage
      const { data, error } = await supabase.storage
        .from('fc-china-uploads')
        .list(filePath.split('/').slice(0, -1).join('/'));

      if (error || !data.find(file => filePath.endsWith(file.name))) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'File not found in storage'
        });
      }

      // Process image if needed (resize, optimize)
      if (uploadType.includes('image')) {
        await processImage(filePath);
      }

      // Save file record to database
      const fileRecord = await ctx.db.file.create({
        data: {
          path: filePath,
          type: uploadType,
          factoryId: ctx.user.factoryId!,
          uploadedBy: ctx.user.id,
          metadata: metadata || {},
          publicUrl: `${process.env.SUPABASE_URL}/storage/v1/object/public/fc-china-uploads/${filePath}`
        }
      });

      return fileRecord;
    })
});

// Image processing utility
async function processImage(filePath: string) {
  // Implementation for image optimization
  // - Resize to multiple sizes (thumbnail, medium, large)
  // - Compress images
  // - Generate WebP versions
  // - Extract metadata (dimensions, etc.)
}
```

### **Real-time Communication**

```typescript
// src/lib/socket.ts
import { Server } from 'socket.io';
import { verifyAuth0Token } from './auth';

export function setupSocketIO(server: any) {
  const io = new Server(server, {
    cors: {
      origin: process.env.FRONTEND_URL,
      credentials: true
    }
  });
  
  // Authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      const decoded = await verifyAuth0Token(token);
      
      const user = await db.user.findUnique({
        where: { auth0Id: decoded.sub },
        include: { factory: true }
      });
      
      if (!user) {
        return next(new Error('Authentication failed'));
      }
      
      socket.data.user = user;
      socket.data.factory = user.factory;
      next();
    } catch (error) {
      next(new Error('Authentication failed'));
    }
  });
  
  io.on('connection', (socket) => {
    const user = socket.data.user;
    const factory = socket.data.factory;
    
    // Join factory room for factory users
    if (factory) {
      socket.join(`factory:${factory.id}`);
    }
    
    // Join user-specific room
    socket.join(`user:${user.id}`);
    
    // Handle message sending
    socket.on('send_message', async (data) => {
      try {
        const { receiverId, content, type, productId, attachments } = data;
        
        // Validate receiver
        const receiver = await db.user.findUnique({
          where: { id: receiverId }
        });
        
        if (!receiver) {
          socket.emit('error', { message: 'Receiver not found' });
          return;
        }
        
        // Create message
        const message = await db.message.create({
          data: {
            content,
            type: type || 'TEXT',
            senderId: user.id,
            receiverId,
            productId,
            attachments: attachments || [],
            factoryId: factory?.id || receiver.factoryId!
          },
          include: {
            sender: true,
            receiver: true,
            product: true
          }
        });
        
        // Send to receiver
        io.to(`user:${receiverId}`).emit('new_message', message);
        
        // Confirm to sender
        socket.emit('message_sent', message);
        
        // Send push notification if receiver is offline
        await sendPushNotification(receiver, {
          title: `New message from ${user.name}`,
          body: content,
          data: { messageId: message.id }
        });
        
      } catch (error) {
        socket.emit('error', { message: 'Failed to send message' });
      }
    });
    
    // Handle order status updates
    socket.on('order_status_update', async (data) => {
      if (!factory || user.role === 'CUSTOMER') {
        socket.emit('error', { message: 'Unauthorized' });
        return;
      }
      
      try {
        const { orderId, status, notes } = data;
        
        // Update order status (using tRPC procedure)
        const updatedOrder = await updateOrderStatusProcedure({
          orderId,
          status,
          notes
        }, { user, db });
        
        // Notify customer
        io.to(`user:${updatedOrder.customerId}`).emit('order_status_changed', {
          orderId,
          status,
          order: updatedOrder
        });
        
        // Notify factory staff
        io.to(`factory:${factory.id}`).emit('order_updated', updatedOrder);
        
      } catch (error) {
        socket.emit('error', { message: 'Failed to update order status' });
      }
    });
    
    socket.on('disconnect', () => {
      console.log(`User ${user.id} disconnected`);
    });
  });
  
  return io;
}
```

---

## 🔐 **Security Implementation**

### **Authentication Flow**

```typescript
// src/lib/auth.ts
import jwt from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';

const client = jwksClient({
  jwksUri: `https://${process.env.AUTH0_DOMAIN}/.well-known/jwks.json`
});

function getKey(header: any, callback: any) {
  client.getSigningKey(header.kid, (err, key) => {
    const signingKey = key?.getPublicKey();
    callback(null, signingKey);
  });
}

export async function verifyAuth0Token(token: string): Promise<any> {
  return new Promise((resolve, reject) => {
    jwt.verify(token, getKey, {
      audience: process.env.AUTH0_AUDIENCE,
      issuer: `https://${process.env.AUTH0_DOMAIN}/`,
      algorithms: ['RS256']
    }, (err, decoded) => {
      if (err) {
        reject(err);
      } else {
        resolve(decoded);
      }
    });
  });
}

// Multi-tenant access control
export function checkFactoryAccess(userFactoryId: string | null, requestedFactoryId: string) {
  if (!userFactoryId) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'User not associated with any factory'
    });
  }
  
  if (userFactoryId !== requestedFactoryId) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Access denied to this factory'
    });
  }
}
```

### **Row Level Security (RLS) Policies**

```sql
-- Enable RLS on all tables
ALTER TABLE factories ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Factory isolation policies
CREATE POLICY factory_isolation_products ON products
  FOR ALL
  USING (factory_id = current_setting('app.current_factory_id')::text);

CREATE POLICY factory_isolation_orders ON orders
  FOR ALL
  USING (factory_id = current_setting('app.current_factory_id')::text);

CREATE POLICY factory_isolation_messages ON messages
  FOR ALL
  USING (factory_id = current_setting('app.current_factory_id')::text);

-- User access policies
CREATE POLICY user_access_own_data ON users
  FOR ALL
  USING (id = current_setting('app.current_user_id')::text);

-- Customer access to orders
CREATE POLICY customer_order_access ON orders
  FOR SELECT
  USING (customer_id = current_setting('app.current_user_id')::text);
```

---

## 📱 **Mobile App Architecture**

### **Flutter Project Structure**

```
lib/
├── main_factory.dart          # Factory app entry point
├── main_customer.dart         # Customer app entry point
├── shared/                    # Shared code between flavors
│   ├── models/               # Data models
│   ├── services/             # API services
│   ├── providers/            # Riverpod providers
│   ├── widgets/              # Reusable widgets
│   ├── utils/                # Utility functions
│   └── constants/            # App constants
├── factory/                   # Factory-specific code
│   ├── screens/              # Factory screens
│   ├── widgets/              # Factory widgets
│   └── providers/            # Factory providers
├── customer/                  # Customer-specific code
│   ├── screens/              # Customer screens
│   ├── widgets/              # Customer widgets
│   └── providers/            # Customer providers
└── config/                    # Configuration
    ├── app_config.dart       # App configuration
    ├── api_config.dart       # API configuration
    └── theme_config.dart     # Theme configuration
```

### **State Management with Riverpod**

```dart
// lib/shared/providers/auth_provider.dart
@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  AuthState build() {
    return const AuthState.initial();
  }
  
  Future<void> login(String email, String password) async {
    state = const AuthState.loading();
    
    try {
      final result = await ref.read(authServiceProvider).login(email, password);
      state = AuthState.authenticated(result.user, result.token);
      
      // Store token securely
      await ref.read(secureStorageProvider).write(
        key: 'auth_token',
        value: result.token,
      );
    } catch (error) {
      state = AuthState.error(error.toString());
    }
  }
  
  Future<void> logout() async {
    await ref.read(secureStorageProvider).delete(key: 'auth_token');
    state = const AuthState.initial();
  }
}

// lib/shared/providers/products_provider.dart
@riverpod
class ProductsNotifier extends _$ProductsNotifier {
  @override
  Future<List<Product>> build(String factoryId) async {
    final apiService = ref.read(apiServiceProvider);
    return apiService.getProducts(factoryId);
  }
  
  Future<void> createProduct(CreateProductRequest request) async {
    final apiService = ref.read(apiServiceProvider);
    await apiService.createProduct(request);
    
    // Refresh the list
    ref.invalidateSelf();
  }
}
```

### **API Service Layer**

```dart
// lib/shared/services/api_service.dart
class ApiService {
  final Dio _dio;
  final String _baseUrl;
  
  ApiService(this._dio, this._baseUrl);
  
  Future<List<Product>> getProducts(String factoryId, {
    String? search,
    String? categoryId,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/trpc/products.getProducts',
        queryParameters: {
          'input': jsonEncode({
            'factoryId': factoryId,
            'search': search,
            'categoryId': categoryId,
            'page': page,
            'limit': limit,
          }),
        },
      );
      
      final data = response.data['result']['data'];
      return (data['products'] as List)
          .map((json) => Product.fromJson(json))
          .toList();
    } catch (error) {
      throw ApiException('Failed to fetch products: $error');
    }
  }
  
  Future<Product> createProduct(CreateProductRequest request) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/trpc/products.createProduct',
        data: {
          'input': request.toJson(),
        },
      );
      
      return Product.fromJson(response.data['result']['data']);
    } catch (error) {
      throw ApiException('Failed to create product: $error');
    }
  }
}
```

---

## 🚀 **Deployment Architecture**

### **Infrastructure Overview**

```yaml
# docker-compose.yml
version: '3.8'

services:
  api:
    build: ./apps/api
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - AUTH0_DOMAIN=${AUTH0_DOMAIN}
      - AUTH0_AUDIENCE=${AUTH0_AUDIENCE}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    ports:
      - "3001:3001"
    depends_on:
      - redis
    
  web:
    build: ./apps/web
    environment:
      - NEXT_PUBLIC_API_URL=${API_URL}
      - NEXT_PUBLIC_AUTH0_DOMAIN=${AUTH0_DOMAIN}
      - NEXT_PUBLIC_AUTH0_CLIENT_ID=${AUTH0_CLIENT_ID}
    ports:
      - "3000:3000"
    
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

### **CI/CD Pipeline**

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: npm ci
      - run: npm run type-check
      - run: npm run lint
      - run: npm run test
      
  deploy-api:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Railway
        uses: railway-app/railway@v1
        with:
          token: ${{ secrets.RAILWAY_TOKEN }}
          service: fc-china-api
          
  deploy-web:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Vercel
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
```

---

## 🗄️ **Caching Strategy**

### **Multi-Layer Caching Architecture**

```typescript
// src/lib/cache.ts
import Redis from 'ioredis';
import { LRUCache } from 'lru-cache';

// Redis for distributed caching
const redis = new Redis({
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
});

// In-memory cache for frequently accessed data
const memoryCache = new LRUCache<string, any>({
  max: 1000,
  ttl: 1000 * 60 * 5, // 5 minutes
});

export class CacheService {
  // Product catalog caching
  static async getProducts(factoryId: string, filters: any) {
    const cacheKey = `products:${factoryId}:${JSON.stringify(filters)}`;
    
    // Try memory cache first
    let cached = memoryCache.get(cacheKey);
    if (cached) return cached;
    
    // Try Redis cache
    const redisData = await redis.get(cacheKey);
    if (redisData) {
      cached = JSON.parse(redisData);
      memoryCache.set(cacheKey, cached);
      return cached;
    }
    
    return null;
  }
  
  static async setProducts(factoryId: string, filters: any, data: any, ttl = 300) {
    const cacheKey = `products:${factoryId}:${JSON.stringify(filters)}`;
    
    // Set in both caches
    memoryCache.set(cacheKey, data);
    await redis.setex(cacheKey, ttl, JSON.stringify(data));
  }
  
  // User session caching
  static async getUserSession(userId: string) {
    return await redis.get(`session:${userId}`);
  }
  
  static async setUserSession(userId: string, sessionData: any, ttl = 86400) {
    await redis.setex(`session:${userId}`, ttl, JSON.stringify(sessionData));
  }
  
  // Factory settings caching
  static async getFactorySettings(factoryId: string) {
    const cacheKey = `factory:settings:${factoryId}`;
    const cached = await redis.get(cacheKey);
    return cached ? JSON.parse(cached) : null;
  }
  
  // Cache invalidation
  static async invalidateFactory(factoryId: string) {
    const pattern = `*${factoryId}*`;
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
    
    // Clear memory cache
    memoryCache.clear();
  }
}
```

### **Cache Middleware**

```typescript
// src/middleware/cache.ts
export const cacheMiddleware = (ttl: number = 300) => {
  return t.middleware(async ({ path, input, next, ctx }) => {
    // Skip caching for mutations
    if (path.includes('create') || path.includes('update') || path.includes('delete')) {
      return next();
    }
    
    const cacheKey = `api:${path}:${JSON.stringify(input)}:${ctx.user?.id}`;
    
    // Try to get from cache
    const cached = await CacheService.get(cacheKey);
    if (cached) {
      return cached;
    }
    
    // Execute and cache result
    const result = await next();
    await CacheService.set(cacheKey, result, ttl);
    
    return result;
  });
};
```

---

## 📝 **Comprehensive Logging Framework**

### **Structured Logging System**

```typescript
// src/lib/logger.ts
import winston from 'winston';
import { ElasticsearchTransport } from 'winston-elasticsearch';

const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      ...meta,
      environment: process.env.NODE_ENV,
      service: 'fc-china-api',
    });
  })
);

const transports = [
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }),
  new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    format: logFormat
  }),
  new winston.transports.File({
    filename: 'logs/combined.log',
    format: logFormat
  })
];

// Add Elasticsearch transport for production
if (process.env.NODE_ENV === 'production' && process.env.ELASTICSEARCH_URL) {
  transports.push(
    new ElasticsearchTransport({
      level: 'info',
      clientOpts: {
        node: process.env.ELASTICSEARCH_URL,
        auth: {
          username: process.env.ELASTICSEARCH_USERNAME!,
          password: process.env.ELASTICSEARCH_PASSWORD!
        }
      },
      index: 'fc-china-logs'
    })
  );
}

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports,
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' })
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' })
  ]
});

// Audit logging for business events
export class AuditLogger {
  static async logUserAction({
    userId,
    factoryId,
    action,
    entity,
    entityId,
    oldValues,
    newValues,
    ipAddress,
    userAgent
  }: {
    userId: string;
    factoryId?: string;
    action: string;
    entity: string;
    entityId: string;
    oldValues?: any;
    newValues?: any;
    ipAddress?: string;
    userAgent?: string;
  }) {
    logger.info('User action performed', {
      type: 'AUDIT',
      userId,
      factoryId,
      action,
      entity,
      entityId,
      oldValues,
      newValues,
      ipAddress,
      userAgent,
      timestamp: new Date().toISOString()
    });
  }
  
  static async logSecurityEvent({
    type,
    userId,
    ipAddress,
    userAgent,
    details
  }: {
    type: 'LOGIN_SUCCESS' | 'LOGIN_FAILURE' | 'PERMISSION_DENIED' | 'SUSPICIOUS_ACTIVITY';
    userId?: string;
    ipAddress: string;
    userAgent: string;
    details?: any;
  }) {
    logger.warn('Security event', {
      type: 'SECURITY',
      eventType: type,
      userId,
      ipAddress,
      userAgent,
      details,
      timestamp: new Date().toISOString()
    });
  }
}
```

### **Request Logging Middleware**

```typescript
// src/middleware/logging.ts
export const loggingMiddleware = t.middleware(async ({ path, type, input, ctx, next }) => {
  const startTime = Date.now();
  const requestId = generateRequestId();
  
  // Log request start
  logger.info('Request started', {
    requestId,
    path,
    type,
    userId: ctx.user?.id,
    factoryId: ctx.user?.factoryId,
    input: sanitizeInput(input),
    timestamp: new Date().toISOString()
  });
  
  try {
    const result = await next();
    const duration = Date.now() - startTime;
    
    // Log successful request
    logger.info('Request completed', {
      requestId,
      path,
      type,
      duration,
      status: 'success',
      timestamp: new Date().toISOString()
    });
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // Log error
    logger.error('Request failed', {
      requestId,
      path,
      type,
      duration,
      error: error.message,
      stack: error.stack,
      status: 'error',
      timestamp: new Date().toISOString()
    });
    
    throw error;
  }
});
```

---

## 🔒 **Enhanced Security Configuration**

### **Security Headers & CORS**

```typescript
// src/middleware/security.ts
import helmet from 'helmet';
import cors from 'cors';
import rateLimit from 'express-rate-limit';

// Security headers
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'https:', process.env.SUPABASE_URL!],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", process.env.SUPABASE_URL!, 'wss:'],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// CORS configuration
export const corsConfig = cors({
  origin: (origin, callback) => {
    const allowedOrigins = [
      process.env.WEB_URL!,
      process.env.ADMIN_URL!,
      'http://localhost:3000',
      'http://localhost:3001'
    ];
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});

// Rate limiting
export const rateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP',
  standardHeaders: true,
  legacyHeaders: false,
});

// API rate limiting (more restrictive)
export const apiRateLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // 60 requests per minute
  keyGenerator: (req) => {
    // Rate limit by user ID if authenticated, otherwise by IP
    return req.user?.id || req.ip;
  },
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health';
  }
});
```

### **Input Validation & Sanitization**

```typescript
// src/lib/validation.ts
import DOMPurify from 'isomorphic-dompurify';
import validator from 'validator';

export class InputValidator {
  static sanitizeHtml(input: string): string {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
      ALLOWED_ATTR: []
    });
  }
  
  static validateEmail(email: string): boolean {
    return validator.isEmail(email) && email.length <= 254;
  }
  
  static validatePhone(phone: string): boolean {
    return validator.isMobilePhone(phone, 'any', { strictMode: false });
  }
  
  static validateUrl(url: string): boolean {
    return validator.isURL(url, {
      protocols: ['http', 'https'],
      require_protocol: true
    });
  }
  
  static sanitizeFilename(filename: string): string {
    return filename.replace(/[^a-zA-Z0-9.-]/g, '_').substring(0, 255);
  }
  
  static validateFileUpload(file: any): { valid: boolean; error?: string } {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (file.size > maxSize) {
      return { valid: false, error: 'File size exceeds 10MB limit' };
    }
    
    if (!allowedTypes.includes(file.mimetype)) {
      return { valid: false, error: 'File type not allowed' };
    }
    
    return { valid: true };
  }
}
```

---

## 📊 **Enhanced Monitoring & Observability**

### **Error Tracking**

```typescript
// src/lib/monitoring.ts
import * as Sentry from '@sentry/node';
import { ProfilingIntegration } from '@sentry/profiling-node';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  integrations: [
    new ProfilingIntegration(),
  ],
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  environment: process.env.NODE_ENV,
  beforeSend(event) {
    // Filter out sensitive data
    if (event.request?.data) {
      event.request.data = sanitizeSentryData(event.request.data);
    }
    return event;
  }
});

export function captureException(error: Error, context?: any) {
  Sentry.withScope((scope) => {
    if (context) {
      scope.setContext('additional', context);
    }
    Sentry.captureException(error);
  });
}

export function captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info') {
  Sentry.captureMessage(message, level);
}
```

### **Performance Monitoring**

```typescript
// src/middleware/performance.ts
export const performanceMiddleware = t.middleware(async ({ path, type, next }) => {
  const start = Date.now();
  
  try {
    const result = await next();
    const duration = Date.now() - start;
    
    // Log slow queries
    if (duration > 1000) {
      logger.warn(`Slow ${type} ${path}: ${duration}ms`);
    }
    
    // Send metrics to monitoring service
    await sendMetric('api.request.duration', duration, {
      path,
      type,
      status: 'success'
    });
    
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    
    await sendMetric('api.request.duration', duration, {
      path,
      type,
      status: 'error'
    });
    
    throw error;
  }
});
```

---

## 🔄 **Backup & Disaster Recovery**

### **Database Backup Strategy**

```typescript
// src/scripts/backup.ts
import { exec } from 'child_process';
import { promisify } from 'util';
import AWS from 'aws-sdk';

const execAsync = promisify(exec);
const s3 = new AWS.S3();

export class BackupService {
  static async createDatabaseBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = `backup-${timestamp}.sql`;
    
    try {
      // Create database dump
      await execAsync(`pg_dump ${process.env.DATABASE_URL} > ${backupFile}`);
      
      // Upload to S3
      const fileContent = await fs.readFile(backupFile);
      await s3.upload({
        Bucket: process.env.BACKUP_BUCKET!,
        Key: `database-backups/${backupFile}`,
        Body: fileContent,
        ServerSideEncryption: 'AES256'
      }).promise();
      
      // Clean up local file
      await fs.unlink(backupFile);
      
      logger.info('Database backup completed', { backupFile });
    } catch (error) {
      logger.error('Database backup failed', { error: error.message });
      throw error;
    }
  }
  
  static async restoreDatabase(backupFile: string) {
    try {
      // Download from S3
      const object = await s3.getObject({
        Bucket: process.env.BACKUP_BUCKET!,
        Key: `database-backups/${backupFile}`
      }).promise();
      
      // Write to local file
      await fs.writeFile(backupFile, object.Body as Buffer);
      
      // Restore database
      await execAsync(`psql ${process.env.DATABASE_URL} < ${backupFile}`);
      
      // Clean up
      await fs.unlink(backupFile);
      
      logger.info('Database restore completed', { backupFile });
    } catch (error) {
      logger.error('Database restore failed', { error: error.message });
      throw error;
    }
  }
}
```

### **Environment Configuration Management**

```typescript
// src/config/environment.ts
import { z } from 'zod';

const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'staging', 'production']),
  PORT: z.string().transform(Number),
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url(),
  AUTH0_DOMAIN: z.string(),
  AUTH0_AUDIENCE: z.string(),
  SUPABASE_URL: z.string().url(),
  SUPABASE_ANON_KEY: z.string(),
  SUPABASE_SERVICE_KEY: z.string(),
  SENTRY_DSN: z.string().url().optional(),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  BACKUP_BUCKET: z.string().optional(),
  AWS_ACCESS_KEY_ID: z.string().optional(),
  AWS_SECRET_ACCESS_KEY: z.string().optional(),
});

export const env = envSchema.parse(process.env);

// Validate environment on startup
if (env.NODE_ENV === 'production') {
  const requiredProdVars = [
    'SENTRY_DSN',
    'BACKUP_BUCKET',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY'
  ];
  
  for (const varName of requiredProdVars) {
    if (!process.env[varName]) {
      throw new Error(`Missing required production environment variable: ${varName}`);
    }
  }
}
```

---

**This Technical Design Document provides the complete technical foundation for implementing the FC-CHINA platform according to the PRD specifications. All code examples are production-ready and follow the established development rules.**