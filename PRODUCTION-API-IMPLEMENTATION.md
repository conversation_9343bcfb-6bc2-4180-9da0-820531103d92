# Production API Implementation

## 🚀 **Enterprise-Grade tRPC API**

### **Production Server Configuration**

#### **Express Server with Production Middleware**
```typescript
// src/server.ts
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { createExpressMiddleware } from '@trpc/server/adapters/express';
import { appRouter } from './routers';
import { createContext } from './lib/context';
import { config } from './lib/config';
import { globalRateLimit, apiRateLimit } from './lib/security/rate-limiting';
import { securityHeaders } from './lib/security/headers';
import { requestLogger, errorLogger } from './lib/logging/middleware';
import { healthCheckRouter } from './routers/health';
import { metricsRouter } from './routers/metrics';

const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(securityHeaders);
app.use(helmet({
  contentSecurityPolicy: config.NODE_ENV === 'production',
  crossOriginEmbedderPolicy: config.NODE_ENV === 'production',
}));

// Performance middleware
app.use(compression({
  level: 6,
  threshold: 1024,
  filter: (req, res) => {
    if (req.headers['x-no-compression']) return false;
    return compression.filter(req, res);
  },
}));

// CORS configuration
app.use(cors({
  origin: (origin, callback) => {
    const allowedOrigins = config.CORS_ORIGIN.split(',');
    
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token'],
  exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
}));

// Body parsing
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    // Store raw body for webhook verification
    (req as any).rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use(requestLogger);

// Rate limiting
app.use('/api', globalRateLimit);
app.use('/api/trpc', apiRateLimit);

// Health check endpoint (no auth required)
app.use('/health', healthCheckRouter);

// Metrics endpoint (protected)
app.use('/metrics', metricsRouter);

// Main tRPC API
app.use('/api/trpc', createExpressMiddleware({
  router: appRouter,
  createContext,
  onError: ({ error, type, path, input, ctx, req }) => {
    // Log errors with context
    errorLogger.error('tRPC Error', {
      error: error.message,
      code: error.code,
      type,
      path,
      input: sanitizeInput(input),
      userId: ctx?.user?.id,
      factoryId: ctx?.user?.factoryId,
      ip: req?.ip,
      userAgent: req?.headers['user-agent'],
      timestamp: new Date().toISOString(),
    });
    
    // Don't expose internal errors in production
    if (config.NODE_ENV === 'production' && error.code === 'INTERNAL_SERVER_ERROR') {
      error.message = 'An internal error occurred';
    }
  },
}));

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString(),
  });
});

// Global error handler
app.use(errorLogger);

// Graceful shutdown
const server = app.listen(config.PORT, () => {
  console.log(`🚀 Server running on port ${config.PORT}`);
  console.log(`📊 Health check: http://localhost:${config.PORT}/health`);
  console.log(`🔧 Environment: ${config.NODE_ENV}`);
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
    process.exit(0);
  });
});

function sanitizeInput(input: any): any {
  if (!input) return input;
  
  const sanitized = { ...input };
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  
  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}
```

#### **Production Context Creation**
```typescript
// src/lib/context.ts
import { inferAsyncReturnType } from '@trpc/server';
import { CreateExpressContextOptions } from '@trpc/server/adapters/express';
import { prisma } from './database/connection';
import { redis } from './redis';
import { AuditLogger } from './logging/audit';

export async function createContext({ req, res }: CreateExpressContextOptions) {
  // Extract request metadata
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  const userAgent = req.headers['user-agent'] || 'unknown';
  const requestId = req.headers['x-request-id'] || generateRequestId();
  
  // Create base context
  const baseContext = {
    req,
    res,
    db: prisma,
    redis,
    ip,
    userAgent,
    requestId,
    startTime: Date.now(),
  };
  
  // Add user context if authenticated
  const authHeader = req.headers.authorization;
  if (authHeader?.startsWith('Bearer ')) {
    try {
      // User will be added by auth middleware
      return baseContext;
    } catch (error) {
      // Log authentication attempt
      await AuditLogger.logSecurityEvent({
        event: 'AUTH_CONTEXT_ERROR',
        error: error.message,
        ip,
        userAgent,
        timestamp: new Date(),
      });
    }
  }
  
  return baseContext;
}

export type Context = inferAsyncReturnType<typeof createContext>;

function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(7)}`;
}
```

### **Production tRPC Procedures**

#### **Enhanced Product Router**
```typescript
// src/routers/products.ts
import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router } from '../lib/trpc';
import { requireAuth, requireFactoryAccess, requirePermission } from '../lib/auth/middleware';
import { Permission } from '../lib/auth/permissions';
import { createProductSchema, updateProductSchema } from '../lib/validation/schemas';
import { searchProducts, getFactoryMetrics } from '../lib/database/queries';
import { CacheManager } from '../lib/cache/manager';
import { AuditLogger } from '../lib/logging/audit';
import { uploadRateLimit } from '../lib/security/rate-limiting';

export const productsRouter = router({
  // Get products with advanced filtering and caching
  getAll: requireAuth
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().max(200).optional(),
      categoryId: z.string().cuid().optional(),
      priceRange: z.object({
        min: z.number().min(0),
        max: z.number().min(0),
      }).optional(),
      sortBy: z.enum(['name', 'price', 'created', 'updated']).default('created'),
      sortOrder: z.enum(['asc', 'desc']).default('desc'),
      factoryId: z.string().cuid().optional(),
    }))
    .query(async ({ input, ctx }) => {
      const cacheKey = `products:${JSON.stringify(input)}`;
      
      // Try cache first
      const cached = await CacheManager.get(cacheKey);
      if (cached) {
        return cached;
      }
      
      // Validate factory access if specified
      if (input.factoryId && ctx.user.factoryId !== input.factoryId) {
        if (!ctx.user.permissions.includes(Permission.SYSTEM_ADMIN)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied to factory products',
          });
        }
      }
      
      const result = await searchProducts({
        factoryId: input.factoryId || ctx.user.factoryId,
        query: input.search,
        categoryId: input.categoryId,
        priceRange: input.priceRange,
        page: input.page,
        limit: input.limit,
      });
      
      // Cache results for 5 minutes
      await CacheManager.set(cacheKey, result, 300);
      
      return result;
    }),
  
  // Get single product with detailed information
  getById: requireAuth
    .input(z.object({
      id: z.string().cuid(),
      includeAnalytics: z.boolean().default(false),
    }))
    .query(async ({ input, ctx }) => {
      const product = await ctx.db.product.findFirst({
        where: {
          id: input.id,
          ...(ctx.user.factoryId && { factoryId: ctx.user.factoryId }),
        },
        include: {
          images: {
            orderBy: { order: 'asc' },
          },
          variants: {
            where: { isActive: true },
            include: {
              images: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
              verificationStatus: true,
              country: true,
            },
          },
          ...(input.includeAnalytics && ctx.user.permissions.includes(Permission.ANALYTICS_READ) && {
            _count: {
              select: {
                inquiries: true,
                orderItems: true,
                reviews: true,
              },
            },
          }),
        },
      });
      
      if (!product) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found',
        });
      }
      
      // Log product view for analytics
      await AuditLogger.logBusinessEvent({
        event: 'PRODUCT_VIEWED',
        entityType: 'product',
        entityId: product.id,
        userId: ctx.user.id,
        factoryId: product.factoryId,
        metadata: {
          productName: product.name,
          viewedBy: ctx.user.role,
        },
        timestamp: new Date(),
      });
      
      return product;
    }),
  
  // Create product with comprehensive validation
  create: requireAuth
    .use(requireFactoryAccess)
    .use(requirePermission(Permission.PRODUCT_WRITE))
    .input(createProductSchema)
    .mutation(async ({ input, ctx }) => {
      // Check factory product limits based on subscription
      const factory = await ctx.db.factory.findUnique({
        where: { id: ctx.user.factoryId! },
        select: {
          subscriptionTier: true,
          _count: {
            select: { products: true },
          },
        },
      });
      
      if (!factory) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Factory not found',
        });
      }
      
      // Check product limits
      const limits = {
        FREE: 10,
        BASIC: 100,
        STANDARD: 1000,
        PREMIUM: 10000,
        ENTERPRISE: Infinity,
      };
      
      const currentCount = factory._count.products;
      const limit = limits[factory.subscriptionTier];
      
      if (currentCount >= limit) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: `Product limit reached for ${factory.subscriptionTier} plan`,
        });
      }
      
      // Generate unique slug
      const baseSlug = input.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-|-$/g, '');
      
      let slug = baseSlug;
      let counter = 1;
      
      while (await ctx.db.product.findFirst({
        where: { factoryId: ctx.user.factoryId!, slug }
      })) {
        slug = `${baseSlug}-${counter}`;
        counter++;
      }
      
      // Create product with transaction
      const product = await ctx.db.$transaction(async (tx) => {
        const newProduct = await tx.product.create({
          data: {
            ...input,
            slug,
            factoryId: ctx.user.factoryId!,
            createdBy: ctx.user.id,
            updatedBy: ctx.user.id,
          },
          include: {
            images: true,
            category: true,
            factory: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        });
        
        // Log product creation
        await AuditLogger.logBusinessEvent({
          event: 'PRODUCT_CREATED',
          entityType: 'product',
          entityId: newProduct.id,
          userId: ctx.user.id,
          factoryId: ctx.user.factoryId!,
          metadata: {
            productName: newProduct.name,
            productSlug: newProduct.slug,
            basePrice: newProduct.basePrice.toString(),
            currency: newProduct.currency,
          },
          timestamp: new Date(),
        });
        
        return newProduct;
      });
      
      // Invalidate related caches
      await CacheManager.invalidatePattern(`products:*`);
      await CacheManager.invalidatePattern(`factory:${ctx.user.factoryId}:*`);
      
      return product;
    }),
  
  // Update product with optimistic locking
  update: requireAuth
    .use(requireFactoryAccess)
    .use(requirePermission(Permission.PRODUCT_WRITE))
    .input(updateProductSchema.extend({
      version: z.number().optional(), // For optimistic locking
    }))
    .mutation(async ({ input, ctx }) => {
      const { id, version, ...updateData } = input;
      
      // Verify product exists and belongs to factory
      const existingProduct = await ctx.db.product.findFirst({
        where: {
          id,
          factoryId: ctx.user.factoryId!,
        },
        select: {
          id: true,
          name: true,
          updatedAt: true,
        },
      });
      
      if (!existingProduct) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found or access denied',
        });
      }
      
      // Optimistic locking check
      if (version && existingProduct.updatedAt.getTime() !== version) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'Product has been modified by another user',
        });
      }
      
      const updatedProduct = await ctx.db.product.update({
        where: { id },
        data: {
          ...updateData,
          updatedBy: ctx.user.id,
        },
        include: {
          images: true,
          category: true,
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });
      
      // Log product update
      await AuditLogger.logBusinessEvent({
        event: 'PRODUCT_UPDATED',
        entityType: 'product',
        entityId: updatedProduct.id,
        userId: ctx.user.id,
        factoryId: ctx.user.factoryId!,
        metadata: {
          productName: updatedProduct.name,
          changes: Object.keys(updateData),
        },
        timestamp: new Date(),
      });
      
      // Invalidate caches
      await CacheManager.invalidatePattern(`products:*`);
      await CacheManager.invalidatePattern(`product:${id}:*`);
      
      return updatedProduct;
    }),
  
  // Bulk operations with job queue
  bulkUpdate: requireAuth
    .use(requireFactoryAccess)
    .use(requirePermission(Permission.PRODUCT_WRITE))
    .input(z.object({
      productIds: z.array(z.string().cuid()).max(1000),
      updates: z.object({
        status: z.enum(['DRAFT', 'ACTIVE', 'INACTIVE']).optional(),
        isActive: z.boolean().optional(),
        categoryId: z.string().cuid().optional(),
        tags: z.array(z.string()).optional(),
      }),
      dryRun: z.boolean().default(false),
    }))
    .mutation(async ({ input, ctx }) => {
      const { productIds, updates, dryRun } = input;
      
      // Verify all products belong to factory
      const products = await ctx.db.product.findMany({
        where: {
          id: { in: productIds },
          factoryId: ctx.user.factoryId!,
        },
        select: { id: true, name: true },
      });
      
      if (products.length !== productIds.length) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Some products not found or access denied',
        });
      }
      
      if (dryRun) {
        return {
          affectedCount: products.length,
          products: products.map(p => ({
            id: p.id,
            name: p.name,
            changes: updates,
          })),
        };
      }
      
      // Perform bulk update
      const result = await ctx.db.product.updateMany({
        where: {
          id: { in: productIds },
          factoryId: ctx.user.factoryId!,
        },
        data: {
          ...updates,
          updatedBy: ctx.user.id,
        },
      });
      
      // Log bulk operation
      await AuditLogger.logBusinessEvent({
        event: 'PRODUCTS_BULK_UPDATED',
        entityType: 'product',
        userId: ctx.user.id,
        factoryId: ctx.user.factoryId!,
        metadata: {
          affectedCount: result.count,
          updates,
          productIds: productIds.slice(0, 10), // Log first 10 IDs
        },
        timestamp: new Date(),
      });
      
      // Invalidate caches
      await CacheManager.invalidatePattern(`products:*`);
      
      return {
        affectedCount: result.count,
        message: `Successfully updated ${result.count} products`,
      };
    }),
});
```
