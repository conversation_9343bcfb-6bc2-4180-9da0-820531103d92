# Production Database Schema & Performance

## 🗄️ **Enterprise Database Configuration**

### **Production-Optimized Prisma Schema**

```prisma
// prisma/schema.prisma
generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearch", "metrics", "tracing"]
  binaryTargets   = ["native", "linux-openssl-1.1.x"] // For Docker deployment
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Factory Model with Production Optimizations
model Factory {
  id                    String   @id @default(cuid())
  name                  String   @db.VarChar(200)
  slug                  String   @unique @db.VarChar(100)
  email                 String   @unique @db.VarChar(255)
  phone                 String?  @db.VarChar(20)
  website               String?  @db.VarChar(255)
  
  // Address Information
  address               Json
  country               String   @db.VarChar(2) // ISO country code
  timezone              String   @db.VarChar(50)
  
  // Business Information
  industry              String   @db.VarChar(100)
  companySize           CompanySize
  yearEstablished       Int?
  businessLicense       String?  @db.VarChar(100)
  taxId                 String?  @db.VarChar(50)
  
  // Status and Verification
  status                FactoryStatus @default(PENDING_VERIFICATION)
  verificationStatus    VerificationStatus @default(UNVERIFIED)
  verifiedAt            DateTime?
  
  // Subscription and Billing
  subscriptionTier      SubscriptionTier @default(FREE)
  subscriptionStatus    SubscriptionStatus @default(ACTIVE)
  subscriptionExpiresAt DateTime?
  billingEmail          String?  @db.VarChar(255)
  
  // Security Settings
  securitySettings      Json     @default("{}")
  
  // Audit Fields
  createdAt             DateTime @default(now()) @db.Timestamptz
  updatedAt             DateTime @updatedAt @db.Timestamptz
  createdBy             String?
  updatedBy             String?
  
  // Relations
  users                 User[]
  products              Product[]
  orders                Order[]
  categories            Category[]
  messages              Message[]
  files                 File[]
  auditLogs             AuditLog[]
  
  // Indexes for Performance
  @@index([status, createdAt])
  @@index([industry, country])
  @@index([subscriptionTier, subscriptionStatus])
  @@index([verificationStatus])
  @@map("factories")
}

// User Model with Security Enhancements
model User {
  id                    String   @id @default(cuid())
  auth0Id               String   @unique @db.VarChar(100)
  email                 String   @unique @db.VarChar(255)
  emailVerified         Boolean  @default(false)
  
  // Profile Information
  firstName             String?  @db.VarChar(100)
  lastName              String?  @db.VarChar(100)
  avatar                String?  @db.VarChar(500)
  phone                 String?  @db.VarChar(20)
  locale                String   @default("en") @db.VarChar(10)
  timezone              String   @default("UTC") @db.VarChar(50)
  
  // Security Fields
  status                UserStatus @default(ACTIVE)
  role                  UserRole
  lastLoginAt           DateTime? @db.Timestamptz
  lastLoginIp           String?   @db.VarChar(45) // IPv6 compatible
  passwordChangedAt     DateTime? @db.Timestamptz
  mfaEnabled            Boolean   @default(false)
  mfaSecret             String?   @db.VarChar(100) // Encrypted
  
  // Factory Association
  factoryId             String?
  factory               Factory?  @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Preferences
  preferences           Json      @default("{}")
  notificationSettings  Json      @default("{}")
  
  // Audit Fields
  createdAt             DateTime  @default(now()) @db.Timestamptz
  updatedAt             DateTime  @updatedAt @db.Timestamptz
  deletedAt             DateTime? @db.Timestamptz
  
  // Relations
  createdProducts       Product[] @relation("ProductCreatedBy")
  updatedProducts       Product[] @relation("ProductUpdatedBy")
  sentMessages          Message[] @relation("MessageSender")
  receivedMessages      Message[] @relation("MessageReceiver")
  orders                Order[]
  sessions              UserSession[]
  permissions           UserPermission[]
  auditLogs             AuditLog[]
  
  // Indexes for Performance
  @@index([factoryId, status])
  @@index([email, status])
  @@index([role, factoryId])
  @@index([lastLoginAt])
  @@index([deletedAt]) // For soft deletes
  @@map("users")
}

// Product Model with Full-Text Search
model Product {
  id                    String   @id @default(cuid())
  name                  String   @db.VarChar(200)
  slug                  String   @db.VarChar(250)
  description           String?  @db.Text
  shortDescription      String?  @db.VarChar(500)
  
  // Pricing
  basePrice             Decimal  @db.Decimal(12, 2)
  currency              String   @db.VarChar(3) // ISO currency code
  priceValidUntil       DateTime? @db.Timestamptz
  
  // Inventory
  sku                   String?  @db.VarChar(100)
  stockQuantity         Int      @default(0)
  minimumOrderQuantity  Int      @default(1)
  maximumOrderQuantity  Int?
  leadTimeDays          Int      @default(7)
  
  // Product Details
  specifications        Json     @default("{}")
  features              String[] @db.VarChar(100)
  tags                  String[] @db.VarChar(50)
  dimensions            Json?    // Length, width, height, weight
  materials             String[] @db.VarChar(100)
  colors                String[] @db.VarChar(50)
  certifications        String[] @db.VarChar(100)
  
  // SEO and Marketing
  metaTitle             String?  @db.VarChar(200)
  metaDescription       String?  @db.VarChar(500)
  keywords              String[] @db.VarChar(50)
  
  // Status and Visibility
  status                ProductStatus @default(DRAFT)
  isActive              Boolean  @default(true)
  isFeatured            Boolean  @default(false)
  publishedAt           DateTime? @db.Timestamptz
  
  // Factory Association
  factoryId             String
  factory               Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Category Association
  categoryId            String
  category              Category @relation(fields: [categoryId], references: [id])
  
  // Audit Fields
  createdAt             DateTime @default(now()) @db.Timestamptz
  updatedAt             DateTime @updatedAt @db.Timestamptz
  createdBy             String
  createdByUser         User     @relation("ProductCreatedBy", fields: [createdBy], references: [id])
  updatedBy             String?
  updatedByUser         User?    @relation("ProductUpdatedBy", fields: [updatedBy], references: [id])
  
  // Relations
  images                ProductImage[]
  variants              ProductVariant[]
  orderItems            OrderItem[]
  inquiries             ProductInquiry[]
  reviews               ProductReview[]
  
  // Performance Indexes
  @@index([factoryId, status, isActive])
  @@index([categoryId, status, isActive])
  @@index([status, publishedAt])
  @@index([isFeatured, status, isActive])
  @@index([basePrice, currency])
  @@index([tags]) // GIN index for array search
  @@index([features]) // GIN index for array search
  @@unique([factoryId, slug])
  @@unique([factoryId, sku]) // SKU unique per factory
  @@map("products")
}

// Order Model with Financial Tracking
model Order {
  id                    String   @id @default(cuid())
  orderNumber           String   @unique @db.VarChar(50)
  
  // Customer Information
  customerId            String
  customer              User     @relation(fields: [customerId], references: [id])
  customerEmail         String   @db.VarChar(255)
  customerName          String   @db.VarChar(200)
  
  // Factory Information
  factoryId             String
  factory               Factory  @relation(fields: [factoryId], references: [id])
  
  // Order Details
  status                OrderStatus @default(PENDING)
  priority              OrderPriority @default(NORMAL)
  
  // Financial Information
  subtotal              Decimal  @db.Decimal(12, 2)
  taxAmount             Decimal  @db.Decimal(12, 2) @default(0)
  shippingAmount        Decimal  @db.Decimal(12, 2) @default(0)
  discountAmount        Decimal  @db.Decimal(12, 2) @default(0)
  totalAmount           Decimal  @db.Decimal(12, 2)
  currency              String   @db.VarChar(3)
  
  // Payment Information
  paymentStatus         PaymentStatus @default(PENDING)
  paymentMethod         String?  @db.VarChar(50)
  paymentReference      String?  @db.VarChar(100)
  paidAt                DateTime? @db.Timestamptz
  
  // Shipping Information
  shippingAddress       Json
  shippingMethod        String?  @db.VarChar(100)
  trackingNumber        String?  @db.VarChar(100)
  estimatedDelivery     DateTime? @db.Timestamptz
  shippedAt             DateTime? @db.Timestamptz
  deliveredAt           DateTime? @db.Timestamptz
  
  // Order Metadata
  notes                 String?  @db.Text
  internalNotes         String?  @db.Text
  source                String   @default("web") @db.VarChar(50)
  
  // Audit Fields
  createdAt             DateTime @default(now()) @db.Timestamptz
  updatedAt             DateTime @updatedAt @db.Timestamptz
  confirmedAt           DateTime? @db.Timestamptz
  cancelledAt           DateTime? @db.Timestamptz
  
  // Relations
  items                 OrderItem[]
  payments              Payment[]
  statusHistory         OrderStatusHistory[]
  messages              Message[]
  
  // Performance Indexes
  @@index([factoryId, status, createdAt])
  @@index([customerId, status, createdAt])
  @@index([status, priority, createdAt])
  @@index([paymentStatus, createdAt])
  @@index([orderNumber])
  @@map("orders")
}

// Enums for Type Safety
enum FactoryStatus {
  PENDING_VERIFICATION
  ACTIVE
  SUSPENDED
  INACTIVE
  DELETED
}

enum VerificationStatus {
  UNVERIFIED
  PENDING
  VERIFIED
  REJECTED
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  DELETED
}

enum UserRole {
  SYSTEM_ADMIN
  FACTORY_ADMIN
  FACTORY_USER
  CUSTOMER
  SUPPORT_AGENT
}

enum ProductStatus {
  DRAFT
  PENDING_APPROVAL
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
  DISCONTINUED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  REFUNDED
  CANCELLED
}

enum SubscriptionTier {
  FREE
  BASIC
  STANDARD
  PREMIUM
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  PAST_DUE
  CANCELLED
  EXPIRED
}

enum CompanySize {
  STARTUP
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

enum OrderPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}
```

### **Database Performance Optimizations**

#### **Connection Pool Configuration**
```typescript
// src/lib/database/connection.ts
import { PrismaClient } from '@prisma/client';
import { config } from '../config';

const prismaConfig = {
  datasources: {
    db: {
      url: config.DATABASE_URL,
    },
  },
  
  log: config.NODE_ENV === 'production' 
    ? ['error'] 
    : ['query', 'info', 'warn', 'error'],
    
  errorFormat: config.NODE_ENV === 'production' ? 'minimal' : 'pretty',
  
  // Connection pool settings
  __internal: {
    engine: {
      connectionLimit: config.NODE_ENV === 'production' ? 50 : 10,
      poolTimeout: 30000,
      binaryTargets: ['native', 'linux-openssl-1.1.x'],
    },
  },
} as const;

export const prisma = new PrismaClient(prismaConfig);

// Connection health monitoring
export async function checkDatabaseHealth(): Promise<{
  healthy: boolean;
  latency: number;
  activeConnections?: number;
}> {
  const start = Date.now();
  
  try {
    await prisma.$queryRaw`SELECT 1`;
    const latency = Date.now() - start;
    
    // Get connection pool stats in production
    const stats = config.NODE_ENV === 'production' 
      ? await prisma.$queryRaw`
          SELECT 
            count(*) as active_connections,
            max_conn,
            used_conn
          FROM pg_stat_activity 
          CROSS JOIN (
            SELECT setting::int as max_conn FROM pg_settings WHERE name = 'max_connections'
          ) mc
          CROSS JOIN (
            SELECT count(*) as used_conn FROM pg_stat_activity WHERE state = 'active'
          ) uc
          WHERE state = 'active'
        `
      : null;
    
    return {
      healthy: true,
      latency,
      activeConnections: stats?.[0]?.active_connections,
    };
  } catch (error) {
    return {
      healthy: false,
      latency: Date.now() - start,
    };
  }
}
```

#### **Query Optimization Utilities**
```typescript
// src/lib/database/queries.ts
import { Prisma } from '@prisma/client';
import { prisma } from './connection';

// Optimized product search with full-text search
export async function searchProducts({
  factoryId,
  query,
  categoryId,
  priceRange,
  page = 1,
  limit = 20,
}: {
  factoryId?: string;
  query?: string;
  categoryId?: string;
  priceRange?: { min: number; max: number };
  page: number;
  limit: number;
}) {
  const skip = (page - 1) * limit;
  
  // Build where clause
  const where: Prisma.ProductWhereInput = {
    status: 'ACTIVE',
    isActive: true,
    ...(factoryId && { factoryId }),
    ...(categoryId && { categoryId }),
    ...(priceRange && {
      basePrice: {
        gte: priceRange.min,
        lte: priceRange.max,
      },
    }),
    ...(query && {
      OR: [
        { name: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { tags: { has: query } },
        { features: { has: query } },
      ],
    }),
  };
  
  // Execute optimized queries in parallel
  const [products, total] = await Promise.all([
    prisma.product.findMany({
      where,
      skip,
      take: limit,
      select: {
        id: true,
        name: true,
        slug: true,
        shortDescription: true,
        basePrice: true,
        currency: true,
        stockQuantity: true,
        minimumOrderQuantity: true,
        leadTimeDays: true,
        isFeatured: true,
        images: {
          select: {
            id: true,
            url: true,
            alt: true,
            isPrimary: true,
          },
          where: { isPrimary: true },
          take: 1,
        },
        factory: {
          select: {
            id: true,
            name: true,
            slug: true,
            verificationStatus: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy: [
        { isFeatured: 'desc' },
        { createdAt: 'desc' },
      ],
    }),
    
    prisma.product.count({ where }),
  ]);
  
  return {
    products,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1,
    },
  };
}

// Optimized factory dashboard metrics
export async function getFactoryMetrics(factoryId: string) {
  const [
    totalProducts,
    activeProducts,
    totalOrders,
    pendingOrders,
    monthlyRevenue,
    recentOrders,
  ] = await Promise.all([
    prisma.product.count({
      where: { factoryId },
    }),
    
    prisma.product.count({
      where: { factoryId, status: 'ACTIVE', isActive: true },
    }),
    
    prisma.order.count({
      where: { factoryId },
    }),
    
    prisma.order.count({
      where: { factoryId, status: { in: ['PENDING', 'CONFIRMED'] } },
    }),
    
    prisma.order.aggregate({
      where: {
        factoryId,
        status: 'DELIVERED',
        createdAt: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        },
      },
      _sum: { totalAmount: true },
    }),
    
    prisma.order.findMany({
      where: { factoryId },
      select: {
        id: true,
        orderNumber: true,
        status: true,
        totalAmount: true,
        currency: true,
        createdAt: true,
        customer: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    }),
  ]);
  
  return {
    products: {
      total: totalProducts,
      active: activeProducts,
    },
    orders: {
      total: totalOrders,
      pending: pendingOrders,
    },
    revenue: {
      monthly: monthlyRevenue._sum.totalAmount || 0,
    },
    recentOrders,
  };
}
```
