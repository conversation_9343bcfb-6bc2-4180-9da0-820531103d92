{"name": "@fc-china/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "@trpc/client": "^10.45.2", "@trpc/react-query": "^10.45.2", "@tanstack/react-query": "^4.36.1", "@auth0/nextjs-auth0": "^3.5.0", "@hookform/resolvers": "^3.9.1", "react-hook-form": "^7.53.2", "zod": "^3.23.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "lucide-react": "^0.460.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^8.57.1", "eslint-config-next": "15.4.2"}}