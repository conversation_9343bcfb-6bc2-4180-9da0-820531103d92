{
  "name": "api",
  "version": "1.0.0",
  "description": "FC-CHINA tRPC API Server",
  "main": "src/server.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsx watch src/server.ts",
    "start": "node dist/server.js",
    "type-check": "tsc --noEmit",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:studio": "prisma studio",
    "db:seed": "tsx src/lib/database/seed.ts",
    "validate:env": "tsx src/lib/config/validate.ts",
    "clean": "rm -rf dist && rm -rf .turbo && rm -rf node_modules"
  },
  "dependencies": {
    "@trpc/server": "^10.45.0",
    "@prisma/client": "^5.7.1",
    "prisma": "^5.7.1",
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.1.0",
    "compression": "^1.7.4",
    "express-rate-limit": "^7.1.5",
    "redis": "^4.6.12",
    "winston": "^3.11.0",
    "zod": "^3.22.4",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.2",
    "auth0": "^4.2.0",
    "dotenv": "^16.3.1",
    "uuid": "^9.0.1",

  },
  "devDependencies": {
    "@types/express": "^4.17.21",
    "@types/cors": "^2.8.17",
    "@types/compression": "^1.7.5",
    "@types/bcryptjs": "^2.4.6",
    "@types/jsonwebtoken": "^9.0.5",
    "@types/uuid": "^9.0.7",
    "@types/node": "^20.10.5",
    "@types/jest": "^29.5.8",

    "tsx": "^4.6.2",
    "typescript": "^5.3.3",
    "jest": "^29.7.0",
    "ts-jest": "^29.1.1",
    "eslint": "^8.56.0"
  },
  "engines": {
    "node": ">=18.0.0"
  }
}
