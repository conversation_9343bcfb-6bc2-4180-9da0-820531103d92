{"extends": "@fc-china/typescript-config/base.json", "compilerOptions": {"target": "ES2022", "module": "CommonJS", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/lib/*": ["./lib/*"], "@/routers/*": ["./routers/*"], "@/middleware/*": ["./middleware/*"], "@fc-china/shared-types": ["../../packages/shared-types/src"]}}, "include": ["src/**/*", "prisma/seed.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}