import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure, requirePermission } from '../lib/trpc';
import { UserRoleSchema, UserStatusSchema } from '@fc-china/shared-types';

export const usersRouter = router({
  // Get all users (admin only)
  getAll: protectedProcedure
    .use(requirePermission('USER_READ'))
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().optional(),
      role: UserRoleSchema.optional(),
      status: UserStatusSchema.optional(),
      factoryId: z.string().optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, search, role, status, factoryId } = input;
      const skip = (page - 1) * limit;
      
      // Build where clause
      const where: any = {};
      
      // Factory access control
      if (ctx.user.factoryId && !ctx.user.permissions.includes('SYSTEM_ADMIN')) {
        where.factoryId = ctx.user.factoryId;
      } else if (factoryId) {
        where.factoryId = factoryId;
      }
      
      if (search) {
        where.OR = [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }
      
      if (role) where.role = role;
      if (status) where.status = status;
      
      const [users, total] = await Promise.all([
        ctx.db.user.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            factory: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        }),
        ctx.db.user.count({ where }),
      ]);
      
      return {
        data: users.map(user => ({
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          status: user.status,
          factoryId: user.factoryId,
          avatar: user.avatar,
          phone: user.phone,
          language: user.language,
          factory: user.factory,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    }),
  
  // Get user by ID
  getById: protectedProcedure
    .use(requirePermission('USER_READ'))
    .input(z.object({
      id: z.string().cuid(),
    }))
    .query(async ({ input, ctx }) => {
      const user = await ctx.db.user.findFirst({
        where: {
          id: input.id,
          // Factory access control
          ...(ctx.user.factoryId && !ctx.user.permissions.includes('SYSTEM_ADMIN') && {
            factoryId: ctx.user.factoryId,
          }),
        },
        include: {
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
              status: true,
            },
          },
        },
      });
      
      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }
      
      return {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        factoryId: user.factoryId,
        permissions: user.permissions,
        avatar: user.avatar,
        phone: user.phone,
        language: user.language,
        timezone: user.timezone,
        factory: user.factory,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLoginAt: user.lastLoginAt,
      };
    }),
  
  // Update user
  update: protectedProcedure
    .use(requirePermission('USER_WRITE'))
    .input(z.object({
      id: z.string().cuid(),
      firstName: z.string().min(1).optional(),
      lastName: z.string().min(1).optional(),
      role: UserRoleSchema.optional(),
      status: UserStatusSchema.optional(),
      phone: z.string().optional(),
      language: z.string().optional(),
      timezone: z.string().optional(),
      permissions: z.array(z.string()).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { id, ...updateData } = input;
      
      // Check if user exists and access control
      const existingUser = await ctx.db.user.findFirst({
        where: {
          id,
          // Factory access control
          ...(ctx.user.factoryId && !ctx.user.permissions.includes('SYSTEM_ADMIN') && {
            factoryId: ctx.user.factoryId,
          }),
        },
      });
      
      if (!existingUser) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found or access denied',
        });
      }
      
      // Role hierarchy check - can't promote to higher role
      if (updateData.role) {
        const { getRoleLevel, canManageUser } = await import('@fc-china/shared-types');
        if (!canManageUser(ctx.user.role as any, updateData.role)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Cannot assign higher role',
          });
        }
      }
      
      const updatedUser = await ctx.db.user.update({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
        include: {
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });
      
      // Log user update
      await ctx.db.auditLog.create({
        data: {
          type: 'BUSINESS',
          event: 'USER_UPDATED',
          entityType: 'user',
          entityId: updatedUser.id,
          userId: ctx.user.id,
          factoryId: ctx.user.factoryId,
          ipAddress: ctx.ip,
          userAgent: ctx.userAgent,
          metadata: {
            changes: Object.keys(updateData),
            targetUserId: updatedUser.id,
            targetUserEmail: updatedUser.email,
          },
        },
      });
      
      return {
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        role: updatedUser.role,
        status: updatedUser.status,
        factoryId: updatedUser.factoryId,
        permissions: updatedUser.permissions,
        avatar: updatedUser.avatar,
        phone: updatedUser.phone,
        language: updatedUser.language,
        timezone: updatedUser.timezone,
        factory: updatedUser.factory,
      };
    }),
  
  // Deactivate user (soft delete)
  deactivate: protectedProcedure
    .use(requirePermission('USER_DELETE'))
    .input(z.object({
      id: z.string().cuid(),
    }))
    .mutation(async ({ input, ctx }) => {
      // Check if user exists and access control
      const existingUser = await ctx.db.user.findFirst({
        where: {
          id: input.id,
          // Factory access control
          ...(ctx.user.factoryId && !ctx.user.permissions.includes('SYSTEM_ADMIN') && {
            factoryId: ctx.user.factoryId,
          }),
        },
      });
      
      if (!existingUser) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found or access denied',
        });
      }
      
      // Can't deactivate yourself
      if (existingUser.id === ctx.user.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot deactivate your own account',
        });
      }
      
      const updatedUser = await ctx.db.user.update({
        where: { id: input.id },
        data: {
          status: 'INACTIVE',
          updatedAt: new Date(),
        },
      });
      
      // Log user deactivation
      await ctx.db.auditLog.create({
        data: {
          type: 'SECURITY',
          event: 'USER_DEACTIVATED',
          entityType: 'user',
          entityId: updatedUser.id,
          userId: ctx.user.id,
          factoryId: ctx.user.factoryId,
          ipAddress: ctx.ip,
          userAgent: ctx.userAgent,
          metadata: {
            targetUserId: updatedUser.id,
            targetUserEmail: updatedUser.email,
          },
        },
      });
      
      return { success: true };
    }),
  
  // Get user statistics
  getStats: protectedProcedure
    .use(requirePermission('USER_READ'))
    .query(async ({ ctx }) => {
      const where: any = {};
      
      // Factory access control
      if (ctx.user.factoryId && !ctx.user.permissions.includes('SYSTEM_ADMIN')) {
        where.factoryId = ctx.user.factoryId;
      }
      
      const [
        totalUsers,
        activeUsers,
        usersByRole,
        recentUsers,
      ] = await Promise.all([
        ctx.db.user.count({ where }),
        ctx.db.user.count({ where: { ...where, status: 'ACTIVE' } }),
        ctx.db.user.groupBy({
          by: ['role'],
          where,
          _count: { role: true },
        }),
        ctx.db.user.count({
          where: {
            ...where,
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
        }),
      ]);
      
      return {
        totalUsers,
        activeUsers,
        inactiveUsers: totalUsers - activeUsers,
        usersByRole: usersByRole.reduce((acc, item) => {
          acc[item.role] = item._count.role;
          return acc;
        }, {} as Record<string, number>),
        recentUsers,
      };
    }),
});
