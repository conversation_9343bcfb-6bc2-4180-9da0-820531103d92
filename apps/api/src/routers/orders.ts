import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure, requirePermission } from '../lib/trpc';

export const ordersRouter = router({
  // Get all orders (factory users see their orders, customers see their orders)
  getAll: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      status: z.enum(['PENDING', 'CONFIRMED', 'IN_PRODUCTION', 'READY_TO_SHIP', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED']).optional(),
      search: z.string().optional(),
      dateFrom: z.date().optional(),
      dateTo: z.date().optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, status, search, dateFrom, dateTo } = input;
      const skip = (page - 1) * limit;
      
      // Build where clause based on user role
      const where: any = {};
      
      if (ctx.user.factoryId) {
        // Factory users see orders for their factory
        where.factoryId = ctx.user.factoryId;
      } else if (ctx.user.role === 'CUSTOMER' || ctx.user.role === 'CUSTOMER_ADMIN') {
        // Customers see orders they created (by email)
        where.customerEmail = ctx.user.email;
      } else if (!ctx.user.permissions.includes('SYSTEM_ADMIN')) {
        // Other users without system admin can't see orders
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        });
      }
      
      if (status) where.status = status;
      
      if (search) {
        where.OR = [
          { orderNumber: { contains: search, mode: 'insensitive' } },
          { customerName: { contains: search, mode: 'insensitive' } },
          { customerEmail: { contains: search, mode: 'insensitive' } },
          { customerCompany: { contains: search, mode: 'insensitive' } },
        ];
      }
      
      if (dateFrom || dateTo) {
        where.orderDate = {};
        if (dateFrom) where.orderDate.gte = dateFrom;
        if (dateTo) where.orderDate.lte = dateTo;
      }
      
      const [orders, total] = await Promise.all([
        ctx.db.order.findMany({
          where,
          skip,
          take: limit,
          orderBy: { orderDate: 'desc' },
          include: {
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    slug: true,
                    images: {
                      where: { isMain: true },
                      take: 1,
                    },
                  },
                },
                variant: {
                  select: {
                    id: true,
                    name: true,
                    attributes: true,
                  },
                },
              },
            },
            factory: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            assignedTo: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        }),
        ctx.db.order.count({ where }),
      ]);
      
      return {
        data: orders.map(order => ({
          id: order.id,
          orderNumber: order.orderNumber,
          status: order.status,
          totalAmount: Number(order.totalAmount),
          currency: order.currency,
          customerName: order.customerName,
          customerEmail: order.customerEmail,
          customerPhone: order.customerPhone,
          customerCompany: order.customerCompany,
          shippingAddress: {
            street: order.shippingStreet,
            city: order.shippingCity,
            state: order.shippingState,
            postalCode: order.shippingPostalCode,
            country: order.shippingCountry,
          },
          orderDate: order.orderDate,
          requiredDate: order.requiredDate,
          shippedDate: order.shippedDate,
          deliveredDate: order.deliveredDate,
          itemCount: order.items.length,
          items: order.items.map(item => ({
            id: item.id,
            quantity: item.quantity,
            unitPrice: Number(item.unitPrice),
            totalPrice: Number(item.totalPrice),
            product: {
              ...item.product,
              mainImage: item.product.images[0]?.url || null,
            },
            variant: item.variant,
            specifications: item.specifications,
          })),
          factory: order.factory,
          assignedTo: order.assignedTo,
          createdAt: order.createdAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    }),
  
  // Get order by ID
  getById: protectedProcedure
    .input(z.object({
      id: z.string().cuid(),
    }))
    .query(async ({ input, ctx }) => {
      // Build where clause based on user role
      const where: any = { id: input.id };
      
      if (ctx.user.factoryId) {
        where.factoryId = ctx.user.factoryId;
      } else if (ctx.user.role === 'CUSTOMER' || ctx.user.role === 'CUSTOMER_ADMIN') {
        where.customerEmail = ctx.user.email;
      } else if (!ctx.user.permissions.includes('SYSTEM_ADMIN')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        });
      }
      
      const order = await ctx.db.order.findFirst({
        where,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  images: {
                    where: { isMain: true },
                    take: 1,
                  },
                },
              },
              variant: {
                select: {
                  id: true,
                  name: true,
                  attributes: true,
                },
              },
            },
          },
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
              email: true,
              phone: true,
              addressStreet: true,
              addressCity: true,
              addressState: true,
              addressPostalCode: true,
              addressCountry: true,
            },
          },
          assignedTo: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          statusHistory: {
            orderBy: { createdAt: 'desc' },
          },
          messages: {
            orderBy: { createdAt: 'desc' },
            take: 10,
            include: {
              sender: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  role: true,
                },
              },
            },
          },
        },
      });
      
      if (!order) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Order not found',
        });
      }
      
      return {
        id: order.id,
        orderNumber: order.orderNumber,
        status: order.status,
        totalAmount: Number(order.totalAmount),
        currency: order.currency,
        customerName: order.customerName,
        customerEmail: order.customerEmail,
        customerPhone: order.customerPhone,
        customerCompany: order.customerCompany,
        shippingAddress: {
          street: order.shippingStreet,
          city: order.shippingCity,
          state: order.shippingState,
          postalCode: order.shippingPostalCode,
          country: order.shippingCountry,
        },
        billingAddress: order.billingStreet ? {
          street: order.billingStreet,
          city: order.billingCity!,
          state: order.billingState!,
          postalCode: order.billingPostalCode!,
          country: order.billingCountry!,
        } : null,
        notes: order.notes,
        internalNotes: order.internalNotes,
        orderDate: order.orderDate,
        requiredDate: order.requiredDate,
        shippedDate: order.shippedDate,
        deliveredDate: order.deliveredDate,
        items: order.items.map(item => ({
          id: item.id,
          quantity: item.quantity,
          unitPrice: Number(item.unitPrice),
          totalPrice: Number(item.totalPrice),
          product: {
            ...item.product,
            mainImage: item.product.images[0]?.url || null,
          },
          variant: item.variant,
          specifications: item.specifications,
        })),
        factory: order.factory,
        assignedTo: order.assignedTo,
        statusHistory: order.statusHistory,
        messages: order.messages.map(message => ({
          id: message.id,
          content: message.content,
          messageType: message.messageType,
          sender: message.sender,
          isRead: message.isRead,
          attachments: message.attachments,
          createdAt: message.createdAt,
        })),
        createdAt: order.createdAt,
        updatedAt: order.updatedAt,
      };
    }),
  
  // Create order (customers and factory users)
  create: protectedProcedure
    .input(z.object({
      factoryId: z.string().cuid(),
      customerName: z.string().min(1),
      customerEmail: z.string().email(),
      customerPhone: z.string().optional(),
      customerCompany: z.string().optional(),
      shippingAddress: z.object({
        street: z.string().min(1),
        city: z.string().min(1),
        state: z.string().min(1),
        postalCode: z.string().min(1),
        country: z.string().min(1),
      }),
      billingAddress: z.object({
        street: z.string().min(1),
        city: z.string().min(1),
        state: z.string().min(1),
        postalCode: z.string().min(1),
        country: z.string().min(1),
      }).optional(),
      items: z.array(z.object({
        productId: z.string().cuid(),
        variantId: z.string().cuid().optional(),
        quantity: z.number().min(1),
        unitPrice: z.number().min(0),
        specifications: z.record(z.any()).optional(),
      })).min(1),
      notes: z.string().optional(),
      requiredDate: z.date().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { items, shippingAddress, billingAddress, ...orderData } = input;
      
      // Verify factory exists and is active
      const factory = await ctx.db.factory.findFirst({
        where: {
          id: input.factoryId,
          status: 'ACTIVE',
        },
      });
      
      if (!factory) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Factory not found or inactive',
        });
      }
      
      // Verify all products belong to the factory
      const productIds = items.map(item => item.productId);
      const products = await ctx.db.product.findMany({
        where: {
          id: { in: productIds },
          factoryId: input.factoryId,
          isActive: true,
          status: 'ACTIVE',
        },
      });
      
      if (products.length !== productIds.length) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Some products are not available',
        });
      }
      
      // Calculate total amount
      const totalAmount = items.reduce((sum, item) => sum + (item.unitPrice * item.quantity), 0);
      
      // Generate order number
      const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substring(7).toUpperCase()}`;
      
      // Create order with items
      const order = await ctx.db.order.create({
        data: {
          ...orderData,
          orderNumber,
          totalAmount,
          currency: factory.currency,
          shippingStreet: shippingAddress.street,
          shippingCity: shippingAddress.city,
          shippingState: shippingAddress.state,
          shippingPostalCode: shippingAddress.postalCode,
          shippingCountry: shippingAddress.country,
          ...(billingAddress && {
            billingStreet: billingAddress.street,
            billingCity: billingAddress.city,
            billingState: billingAddress.state,
            billingPostalCode: billingAddress.postalCode,
            billingCountry: billingAddress.country,
          }),
          createdBy: ctx.user.id,
          items: {
            create: items.map(item => ({
              productId: item.productId,
              variantId: item.variantId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalPrice: item.unitPrice * item.quantity,
              specifications: item.specifications,
            })),
          },
          statusHistory: {
            create: {
              status: 'PENDING',
              notes: 'Order created',
              createdBy: ctx.user.id,
            },
          },
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
            },
          },
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });
      
      // Log order creation
      await ctx.db.auditLog.create({
        data: {
          type: 'BUSINESS',
          event: 'ORDER_CREATED',
          entityType: 'order',
          entityId: order.id,
          userId: ctx.user.id,
          factoryId: input.factoryId,
          ipAddress: ctx.ip,
          userAgent: ctx.userAgent,
          metadata: {
            orderNumber: order.orderNumber,
            totalAmount: order.totalAmount.toString(),
            itemCount: order.items.length,
            customerEmail: order.customerEmail,
          },
        },
      });
      
      return {
        id: order.id,
        orderNumber: order.orderNumber,
        status: order.status,
        totalAmount: Number(order.totalAmount),
        currency: order.currency,
        items: order.items,
        factory: order.factory,
        createdAt: order.createdAt,
      };
    }),
});
