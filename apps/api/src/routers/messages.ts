import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure } from '../lib/trpc';

export const messagesRouter = router({
  // Get messages for a specific context (order or inquiry)
  getByContext: protectedProcedure
    .input(z.object({
      orderId: z.string().cuid().optional(),
      inquiryId: z.string().cuid().optional(),
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(50),
    }))
    .query(async ({ input, ctx }) => {
      const { orderId, inquiryId, page, limit } = input;
      const skip = (page - 1) * limit;
      
      if (!orderId && !inquiryId) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Either orderId or inquiryId must be provided',
        });
      }
      
      // Build where clause with access control
      const where: any = {};
      
      if (orderId) {
        where.orderId = orderId;
        
        // Verify user has access to this order
        const order = await ctx.db.order.findFirst({
          where: {
            id: orderId,
            OR: [
              { factoryId: ctx.user.factoryId },
              { customerEmail: ctx.user.email },
              ...(ctx.user.permissions.includes('SYSTEM_ADMIN') ? [{}] : []),
            ],
          },
        });
        
        if (!order) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Order not found or access denied',
          });
        }
        
        where.factoryId = order.factoryId;
      }
      
      if (inquiryId) {
        where.inquiryId = inquiryId;
        
        // Verify user has access to this inquiry
        const inquiry = await ctx.db.inquiry.findFirst({
          where: {
            id: inquiryId,
            OR: [
              { factoryId: ctx.user.factoryId },
              { customerEmail: ctx.user.email },
              ...(ctx.user.permissions.includes('SYSTEM_ADMIN') ? [{}] : []),
            ],
          },
        });
        
        if (!inquiry) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Inquiry not found or access denied',
          });
        }
        
        where.factoryId = inquiry.factoryId;
      }
      
      const [messages, total] = await Promise.all([
        ctx.db.message.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            sender: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                role: true,
                avatar: true,
              },
            },
            receiver: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                role: true,
                avatar: true,
              },
            },
          },
        }),
        ctx.db.message.count({ where }),
      ]);
      
      // Mark messages as read if user is the receiver
      const unreadMessageIds = messages
        .filter(msg => msg.receiverId === ctx.user.id && !msg.isRead)
        .map(msg => msg.id);
      
      if (unreadMessageIds.length > 0) {
        await ctx.db.message.updateMany({
          where: { id: { in: unreadMessageIds } },
          data: { isRead: true, readAt: new Date() },
        });
      }
      
      return {
        data: messages.reverse().map(message => ({
          id: message.id,
          content: message.content,
          messageType: message.messageType,
          sender: message.sender,
          receiver: message.receiver,
          isRead: message.isRead,
          readAt: message.readAt,
          attachments: message.attachments,
          createdAt: message.createdAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    }),
  
  // Send a message
  send: protectedProcedure
    .input(z.object({
      content: z.string().min(1),
      messageType: z.enum(['TEXT', 'IMAGE', 'FILE', 'SYSTEM']).default('TEXT'),
      receiverId: z.string().cuid().optional(),
      orderId: z.string().cuid().optional(),
      inquiryId: z.string().cuid().optional(),
      attachments: z.array(z.object({
        url: z.string().url(),
        filename: z.string(),
        mimeType: z.string(),
        size: z.number(),
      })).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { orderId, inquiryId, attachments, ...messageData } = input;
      
      if (!orderId && !inquiryId) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Either orderId or inquiryId must be provided',
        });
      }
      
      let factoryId: string;
      let contextData: any = {};
      
      if (orderId) {
        // Verify user has access to this order
        const order = await ctx.db.order.findFirst({
          where: {
            id: orderId,
            OR: [
              { factoryId: ctx.user.factoryId },
              { customerEmail: ctx.user.email },
              ...(ctx.user.permissions.includes('SYSTEM_ADMIN') ? [{}] : []),
            ],
          },
        });
        
        if (!order) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Order not found or access denied',
          });
        }
        
        factoryId = order.factoryId;
        contextData.orderId = orderId;
      } else if (inquiryId) {
        // Verify user has access to this inquiry
        const inquiry = await ctx.db.inquiry.findFirst({
          where: {
            id: inquiryId,
            OR: [
              { factoryId: ctx.user.factoryId },
              { customerEmail: ctx.user.email },
              ...(ctx.user.permissions.includes('SYSTEM_ADMIN') ? [{}] : []),
            ],
          },
        });
        
        if (!inquiry) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Inquiry not found or access denied',
          });
        }
        
        factoryId = inquiry.factoryId;
        contextData.inquiryId = inquiryId;
      } else {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invalid context',
        });
      }
      
      // Create message
      const message = await ctx.db.message.create({
        data: {
          ...messageData,
          ...contextData,
          senderId: ctx.user.id,
          factoryId,
          attachments: attachments || [],
        },
        include: {
          sender: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
              avatar: true,
            },
          },
          receiver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
              avatar: true,
            },
          },
        },
      });
      
      // Log message creation
      await ctx.db.auditLog.create({
        data: {
          type: 'BUSINESS',
          event: 'MESSAGE_SENT',
          entityType: 'message',
          entityId: message.id,
          userId: ctx.user.id,
          factoryId,
          ipAddress: ctx.ip,
          userAgent: ctx.userAgent,
          metadata: {
            messageType: message.messageType,
            hasAttachments: (attachments?.length || 0) > 0,
            contextType: orderId ? 'order' : 'inquiry',
            contextId: orderId || inquiryId,
          },
        },
      });
      
      return {
        id: message.id,
        content: message.content,
        messageType: message.messageType,
        sender: message.sender,
        receiver: message.receiver,
        isRead: message.isRead,
        attachments: message.attachments,
        createdAt: message.createdAt,
      };
    }),
  
  // Get unread message count
  getUnreadCount: protectedProcedure
    .query(async ({ ctx }) => {
      const count = await ctx.db.message.count({
        where: {
          receiverId: ctx.user.id,
          isRead: false,
        },
      });
      
      return { count };
    }),
  
  // Mark messages as read
  markAsRead: protectedProcedure
    .input(z.object({
      messageIds: z.array(z.string().cuid()),
    }))
    .mutation(async ({ input, ctx }) => {
      const result = await ctx.db.message.updateMany({
        where: {
          id: { in: input.messageIds },
          receiverId: ctx.user.id,
          isRead: false,
        },
        data: {
          isRead: true,
          readAt: new Date(),
        },
      });
      
      return { updatedCount: result.count };
    }),
  
  // Get recent conversations
  getConversations: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(50).default(20),
    }))
    .query(async ({ input, ctx }) => {
      // Get recent messages where user is sender or receiver
      const recentMessages = await ctx.db.message.findMany({
        where: {
          OR: [
            { senderId: ctx.user.id },
            { receiverId: ctx.user.id },
          ],
          ...(ctx.user.factoryId && { factoryId: ctx.user.factoryId }),
        },
        orderBy: { createdAt: 'desc' },
        take: input.limit * 2, // Get more to filter unique conversations
        include: {
          sender: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
              avatar: true,
            },
          },
          receiver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              role: true,
              avatar: true,
            },
          },
          order: {
            select: {
              id: true,
              orderNumber: true,
              status: true,
            },
          },
          inquiry: {
            select: {
              id: true,
              subject: true,
              status: true,
            },
          },
        },
      });
      
      // Group by conversation context and get the latest message for each
      const conversationMap = new Map();
      
      for (const message of recentMessages) {
        const contextKey = message.orderId || message.inquiryId || 'direct';
        const otherUserId = message.senderId === ctx.user.id ? message.receiverId : message.senderId;
        const conversationKey = `${contextKey}-${otherUserId}`;
        
        if (!conversationMap.has(conversationKey)) {
          conversationMap.set(conversationKey, {
            lastMessage: message,
            context: message.order || message.inquiry,
            contextType: message.orderId ? 'order' : message.inquiryId ? 'inquiry' : 'direct',
            otherUser: message.senderId === ctx.user.id ? message.receiver : message.sender,
            unreadCount: 0,
          });
        }
      }
      
      // Get unread counts for each conversation
      for (const [key, conversation] of conversationMap) {
        const unreadCount = await ctx.db.message.count({
          where: {
            receiverId: ctx.user.id,
            senderId: conversation.otherUser.id,
            isRead: false,
            ...(conversation.lastMessage.orderId && { orderId: conversation.lastMessage.orderId }),
            ...(conversation.lastMessage.inquiryId && { inquiryId: conversation.lastMessage.inquiryId }),
          },
        });
        
        conversation.unreadCount = unreadCount;
      }
      
      // Convert to array and sort by last message time
      const conversations = Array.from(conversationMap.values())
        .sort((a, b) => b.lastMessage.createdAt.getTime() - a.lastMessage.createdAt.getTime())
        .slice(0, input.limit);
      
      return conversations.map(conv => ({
        contextType: conv.contextType,
        context: conv.context,
        otherUser: conv.otherUser,
        lastMessage: {
          id: conv.lastMessage.id,
          content: conv.lastMessage.content,
          messageType: conv.lastMessage.messageType,
          createdAt: conv.lastMessage.createdAt,
        },
        unreadCount: conv.unreadCount,
      }));
    }),
});
