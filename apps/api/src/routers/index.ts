import { router } from '../lib/trpc';
import { authRouter } from './auth';
import { usersRouter } from './users';
import { factoriesRouter } from './factories';
import { productsRouter } from './products';
import { ordersRouter } from './orders';
import { messagesRouter } from './messages';

// Main application router
export const appRouter = router({
  auth: authRouter,
  users: usersRouter,
  factories: factoriesRouter,
  products: productsRouter,
  orders: ordersRouter,
  messages: messagesRouter,
});

// Export type definition for client
export type AppRouter = typeof appRouter;
