import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure, publicProcedure, requirePermission } from '../lib/trpc';

export const productsRouter = router({
  // Get all products (public for browsing)
  getAll: publicProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().optional(),
      categoryId: z.string().cuid().optional(),
      factoryId: z.string().cuid().optional(),
      priceRange: z.object({
        min: z.number().min(0),
        max: z.number().min(0),
      }).optional(),
      sortBy: z.enum(['name', 'price', 'created', 'updated']).default('created'),
      sortOrder: z.enum(['asc', 'desc']).default('desc'),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, search, categoryId, factoryId, priceRange, sortBy, sortOrder } = input;
      const skip = (page - 1) * limit;
      
      // Build where clause
      const where: any = {
        isActive: true,
        status: 'ACTIVE',
      };
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { tags: { hasSome: [search] } },
        ];
      }
      
      if (categoryId) where.categoryId = categoryId;
      if (factoryId) where.factoryId = factoryId;
      
      if (priceRange) {
        where.basePrice = {
          gte: priceRange.min,
          lte: priceRange.max,
        };
      }
      
      // Build order by clause
      const orderBy: any = {};
      switch (sortBy) {
        case 'name':
          orderBy.name = sortOrder;
          break;
        case 'price':
          orderBy.basePrice = sortOrder;
          break;
        case 'created':
          orderBy.createdAt = sortOrder;
          break;
        case 'updated':
          orderBy.updatedAt = sortOrder;
          break;
      }
      
      const [products, total] = await Promise.all([
        ctx.db.product.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            images: {
              where: { isMain: true },
              take: 1,
            },
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            factory: {
              select: {
                id: true,
                name: true,
                slug: true,
                verificationStatus: true,
                addressCountry: true,
              },
            },
            _count: {
              select: {
                reviews: { where: { isApproved: true } },
              },
            },
          },
        }),
        ctx.db.product.count({ where }),
      ]);
      
      return {
        data: products.map(product => ({
          id: product.id,
          name: product.name,
          slug: product.slug,
          description: product.description,
          shortDescription: product.shortDescription,
          basePrice: Number(product.basePrice),
          currency: product.currency,
          minOrderQty: product.minOrderQty,
          maxOrderQty: product.maxOrderQty,
          sku: product.sku,
          model: product.model,
          brand: product.brand,
          weight: product.weight ? Number(product.weight) : null,
          dimensions: product.dimensions,
          materials: product.materials,
          colors: product.colors,
          tags: product.tags,
          stockStatus: product.stockStatus,
          isFeatured: product.isFeatured,
          mainImage: product.images[0]?.url || null,
          category: product.category,
          factory: product.factory,
          reviewCount: product._count.reviews,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    }),
  
  // Get product by ID or slug
  getByIdOrSlug: publicProcedure
    .input(z.object({
      identifier: z.string(), // Can be ID or slug
      factoryId: z.string().cuid().optional(),
    }))
    .query(async ({ input, ctx }) => {
      const product = await ctx.db.product.findFirst({
        where: {
          OR: [
            { id: input.identifier },
            { slug: input.identifier },
          ],
          ...(input.factoryId && { factoryId: input.factoryId }),
          isActive: true,
          status: 'ACTIVE',
        },
        include: {
          images: {
            orderBy: { sortOrder: 'asc' },
          },
          variants: {
            where: { isActive: true },
            include: {
              images: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
              verificationStatus: true,
              addressCountry: true,
              email: true,
              phone: true,
              website: true,
            },
          },
          reviews: {
            where: { isApproved: true },
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
          _count: {
            select: {
              reviews: { where: { isApproved: true } },
              orderItems: true,
            },
          },
        },
      });
      
      if (!product) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Product not found',
        });
      }
      
      // Calculate average rating
      const avgRating = product.reviews.length > 0
        ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
        : 0;
      
      return {
        id: product.id,
        name: product.name,
        slug: product.slug,
        description: product.description,
        shortDescription: product.shortDescription,
        basePrice: Number(product.basePrice),
        currency: product.currency,
        minOrderQty: product.minOrderQty,
        maxOrderQty: product.maxOrderQty,
        sku: product.sku,
        model: product.model,
        brand: product.brand,
        weight: product.weight ? Number(product.weight) : null,
        dimensions: product.dimensions,
        materials: product.materials,
        colors: product.colors,
        tags: product.tags,
        stockQuantity: product.stockQuantity,
        stockStatus: product.stockStatus,
        metaTitle: product.metaTitle,
        metaDescription: product.metaDescription,
        isFeatured: product.isFeatured,
        images: product.images,
        variants: product.variants,
        category: product.category,
        factory: product.factory,
        reviews: product.reviews,
        reviewCount: product._count.reviews,
        orderCount: product._count.orderItems,
        avgRating: Math.round(avgRating * 10) / 10,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
      };
    }),
  
  // Create product (factory users only)
  create: protectedProcedure
    .use(requirePermission('PRODUCT_WRITE'))
    .input(z.object({
      name: z.string().min(1),
      description: z.string().optional(),
      shortDescription: z.string().optional(),
      basePrice: z.number().min(0),
      currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).default('USD'),
      minOrderQty: z.number().min(1).default(1),
      maxOrderQty: z.number().min(1).optional(),
      categoryId: z.string().cuid(),
      sku: z.string().optional(),
      model: z.string().optional(),
      brand: z.string().optional(),
      weight: z.number().min(0).optional(),
      dimensions: z.object({
        length: z.number(),
        width: z.number(),
        height: z.number(),
        unit: z.string(),
      }).optional(),
      materials: z.array(z.string()).default([]),
      colors: z.array(z.string()).default([]),
      tags: z.array(z.string()).default([]),
      stockQuantity: z.number().min(0).default(0),
      metaTitle: z.string().optional(),
      metaDescription: z.string().optional(),
      images: z.array(z.object({
        url: z.string().url(),
        alt: z.string().optional(),
        caption: z.string().optional(),
        isMain: z.boolean().default(false),
      })).default([]),
    }))
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }
      
      // Verify category belongs to factory
      const category = await ctx.db.category.findFirst({
        where: {
          id: input.categoryId,
          factoryId: ctx.user.factoryId,
        },
      });
      
      if (!category) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Category not found or access denied',
        });
      }
      
      // Generate unique slug
      const baseSlug = input.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-|-$/g, '');
      
      let slug = baseSlug;
      let counter = 1;
      
      while (await ctx.db.product.findFirst({
        where: { factoryId: ctx.user.factoryId, slug }
      })) {
        slug = `${baseSlug}-${counter}`;
        counter++;
      }
      
      // Create product with images
      const { images, ...productData } = input;
      
      const product = await ctx.db.product.create({
        data: {
          ...productData,
          slug,
          factoryId: ctx.user.factoryId,
          createdBy: ctx.user.id,
          updatedBy: ctx.user.id,
          images: {
            create: images.map((image, index) => ({
              ...image,
              sortOrder: index,
            })),
          },
        },
        include: {
          images: true,
          category: true,
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });
      
      // Log product creation
      await ctx.db.auditLog.create({
        data: {
          type: 'BUSINESS',
          event: 'PRODUCT_CREATED',
          entityType: 'product',
          entityId: product.id,
          userId: ctx.user.id,
          factoryId: ctx.user.factoryId,
          ipAddress: ctx.ip,
          userAgent: ctx.userAgent,
          metadata: {
            productName: product.name,
            productSlug: product.slug,
            basePrice: product.basePrice.toString(),
            currency: product.currency,
          },
        },
      });
      
      return {
        id: product.id,
        name: product.name,
        slug: product.slug,
        description: product.description,
        basePrice: Number(product.basePrice),
        currency: product.currency,
        stockStatus: product.stockStatus,
        images: product.images,
        category: product.category,
        factory: product.factory,
        createdAt: product.createdAt,
      };
    }),
  
  // Get products for factory management
  getFactoryProducts: protectedProcedure
    .use(requirePermission('PRODUCT_READ'))
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().optional(),
      status: z.enum(['DRAFT', 'ACTIVE', 'INACTIVE', 'OUT_OF_STOCK', 'DISCONTINUED']).optional(),
      categoryId: z.string().cuid().optional(),
    }))
    .query(async ({ input, ctx }) => {
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }
      
      const { page, limit, search, status, categoryId } = input;
      const skip = (page - 1) * limit;
      
      const where: any = {
        factoryId: ctx.user.factoryId,
      };
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { sku: { contains: search, mode: 'insensitive' } },
        ];
      }
      
      if (status) where.status = status;
      if (categoryId) where.categoryId = categoryId;
      
      const [products, total] = await Promise.all([
        ctx.db.product.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            images: {
              where: { isMain: true },
              take: 1,
            },
            category: {
              select: {
                id: true,
                name: true,
              },
            },
            _count: {
              select: {
                orderItems: true,
                reviews: true,
              },
            },
          },
        }),
        ctx.db.product.count({ where }),
      ]);
      
      return {
        data: products.map(product => ({
          id: product.id,
          name: product.name,
          slug: product.slug,
          basePrice: Number(product.basePrice),
          currency: product.currency,
          stockQuantity: product.stockQuantity,
          stockStatus: product.stockStatus,
          status: product.status,
          isActive: product.isActive,
          isFeatured: product.isFeatured,
          mainImage: product.images[0]?.url || null,
          category: product.category,
          orderCount: product._count.orderItems,
          reviewCount: product._count.reviews,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    }),
});
