import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure, requirePermission } from '../lib/trpc';

export const factoriesRouter = router({
  // Get all factories (public for customers, filtered for factory users)
  getAll: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().optional(),
      country: z.string().optional(),
      verificationStatus: z.enum(['UNVERIFIED', 'PENDING', 'VERIFIED', 'REJECTED']).optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, search, country, verificationStatus } = input;
      const skip = (page - 1) * limit;
      
      // Build where clause
      const where: any = {};
      
      // Factory users can only see their own factory
      if (ctx.user.factoryId && !ctx.user.permissions.includes('SYSTEM_ADMIN')) {
        where.id = ctx.user.factoryId;
      } else {
        // Customers and admins can see all active factories
        where.status = 'ACTIVE';
      }
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }
      
      if (country) where.addressCountry = country;
      if (verificationStatus) where.verificationStatus = verificationStatus;
      
      const [factories, total] = await Promise.all([
        ctx.db.factory.findMany({
          where,
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            _count: {
              select: {
                products: { where: { isActive: true } },
                users: { where: { status: 'ACTIVE' } },
              },
            },
          },
        }),
        ctx.db.factory.count({ where }),
      ]);
      
      return {
        data: factories.map(factory => ({
          id: factory.id,
          name: factory.name,
          slug: factory.slug,
          description: factory.description,
          logo: factory.logo,
          coverImage: factory.coverImage,
          email: factory.email,
          phone: factory.phone,
          website: factory.website,
          address: {
            street: factory.addressStreet,
            city: factory.addressCity,
            state: factory.addressState,
            postalCode: factory.addressPostalCode,
            country: factory.addressCountry,
          },
          coordinates: factory.latitude && factory.longitude ? {
            latitude: factory.latitude,
            longitude: factory.longitude,
          } : null,
          verificationStatus: factory.verificationStatus,
          status: factory.status,
          establishedYear: factory.establishedYear,
          employeeCount: factory.employeeCount,
          productCount: factory._count.products,
          userCount: factory._count.users,
          createdAt: factory.createdAt,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      };
    }),
  
  // Get factory by ID or slug
  getByIdOrSlug: protectedProcedure
    .input(z.object({
      identifier: z.string(), // Can be ID or slug
    }))
    .query(async ({ input, ctx }) => {
      const factory = await ctx.db.factory.findFirst({
        where: {
          OR: [
            { id: input.identifier },
            { slug: input.identifier },
          ],
          // Factory users can only see their own factory
          ...(ctx.user.factoryId && !ctx.user.permissions.includes('SYSTEM_ADMIN') && {
            id: ctx.user.factoryId,
          }),
        },
        include: {
          _count: {
            select: {
              products: { where: { isActive: true } },
              users: { where: { status: 'ACTIVE' } },
              orders: true,
            },
          },
        },
      });
      
      if (!factory) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Factory not found',
        });
      }
      
      return {
        id: factory.id,
        name: factory.name,
        slug: factory.slug,
        description: factory.description,
        logo: factory.logo,
        coverImage: factory.coverImage,
        businessLicense: factory.businessLicense,
        taxId: factory.taxId,
        establishedYear: factory.establishedYear,
        employeeCount: factory.employeeCount,
        annualRevenue: factory.annualRevenue,
        email: factory.email,
        phone: factory.phone,
        website: factory.website,
        address: {
          street: factory.addressStreet,
          city: factory.addressCity,
          state: factory.addressState,
          postalCode: factory.addressPostalCode,
          country: factory.addressCountry,
        },
        coordinates: factory.latitude && factory.longitude ? {
          latitude: factory.latitude,
          longitude: factory.longitude,
        } : null,
        currency: factory.currency,
        timezone: factory.timezone,
        language: factory.language,
        verificationStatus: factory.verificationStatus,
        status: factory.status,
        subscriptionTier: factory.subscriptionTier,
        subscriptionEndsAt: factory.subscriptionEndsAt,
        productCount: factory._count.products,
        userCount: factory._count.users,
        orderCount: factory._count.orders,
        createdAt: factory.createdAt,
        updatedAt: factory.updatedAt,
      };
    }),
  
  // Update factory (factory admin only)
  update: protectedProcedure
    .use(requirePermission('FACTORY_WRITE'))
    .input(z.object({
      name: z.string().min(1).optional(),
      description: z.string().optional(),
      logo: z.string().url().optional(),
      coverImage: z.string().url().optional(),
      phone: z.string().optional(),
      website: z.string().url().optional(),
      addressStreet: z.string().optional(),
      addressCity: z.string().optional(),
      addressState: z.string().optional(),
      addressPostalCode: z.string().optional(),
      addressCountry: z.string().optional(),
      latitude: z.number().optional(),
      longitude: z.number().optional(),
      establishedYear: z.number().min(1800).max(new Date().getFullYear()).optional(),
      employeeCount: z.number().min(1).optional(),
      annualRevenue: z.number().min(0).optional(),
      currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW', 'CAD', 'AUD']).optional(),
      timezone: z.string().optional(),
      language: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user.factoryId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }
      
      // Generate slug if name is being updated
      let slug: string | undefined;
      if (input.name) {
        const baseSlug = input.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-|-$/g, '');
        
        // Check if slug is unique
        const existingFactory = await ctx.db.factory.findFirst({
          where: {
            slug: baseSlug,
            NOT: { id: ctx.user.factoryId },
          },
        });
        
        if (existingFactory) {
          // Add random suffix if slug exists
          slug = `${baseSlug}-${Math.random().toString(36).substring(7)}`;
        } else {
          slug = baseSlug;
        }
      }
      
      const updatedFactory = await ctx.db.factory.update({
        where: { id: ctx.user.factoryId },
        data: {
          ...input,
          ...(slug && { slug }),
          updatedAt: new Date(),
        },
      });
      
      // Log factory update
      await ctx.db.auditLog.create({
        data: {
          type: 'BUSINESS',
          event: 'FACTORY_UPDATED',
          entityType: 'factory',
          entityId: updatedFactory.id,
          userId: ctx.user.id,
          factoryId: ctx.user.factoryId,
          ipAddress: ctx.ip,
          userAgent: ctx.userAgent,
          metadata: {
            changes: Object.keys(input),
          },
        },
      });
      
      return {
        id: updatedFactory.id,
        name: updatedFactory.name,
        slug: updatedFactory.slug,
        description: updatedFactory.description,
        logo: updatedFactory.logo,
        coverImage: updatedFactory.coverImage,
        email: updatedFactory.email,
        phone: updatedFactory.phone,
        website: updatedFactory.website,
        address: {
          street: updatedFactory.addressStreet,
          city: updatedFactory.addressCity,
          state: updatedFactory.addressState,
          postalCode: updatedFactory.addressPostalCode,
          country: updatedFactory.addressCountry,
        },
        coordinates: updatedFactory.latitude && updatedFactory.longitude ? {
          latitude: updatedFactory.latitude,
          longitude: updatedFactory.longitude,
        } : null,
        verificationStatus: updatedFactory.verificationStatus,
        status: updatedFactory.status,
        updatedAt: updatedFactory.updatedAt,
      };
    }),
  
  // Get factory statistics
  getStats: protectedProcedure
    .use(requirePermission('FACTORY_READ'))
    .query(async ({ ctx }) => {
      if (!ctx.user.factoryId && !ctx.user.permissions.includes('SYSTEM_ADMIN')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Factory access required',
        });
      }
      
      const factoryId = ctx.user.factoryId;
      const isSystemAdmin = ctx.user.permissions.includes('SYSTEM_ADMIN');
      
      const where = isSystemAdmin ? {} : { factoryId };
      
      const [
        totalProducts,
        activeProducts,
        totalOrders,
        recentOrders,
        totalUsers,
        activeUsers,
      ] = await Promise.all([
        ctx.db.product.count({ where }),
        ctx.db.product.count({ where: { ...where, isActive: true } }),
        ctx.db.order.count({ where }),
        ctx.db.order.count({
          where: {
            ...where,
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
        }),
        ctx.db.user.count({ where: isSystemAdmin ? {} : { factoryId } }),
        ctx.db.user.count({ 
          where: { 
            ...(isSystemAdmin ? {} : { factoryId }), 
            status: 'ACTIVE' 
          } 
        }),
      ]);
      
      return {
        products: {
          total: totalProducts,
          active: activeProducts,
          inactive: totalProducts - activeProducts,
        },
        orders: {
          total: totalOrders,
          recent: recentOrders,
        },
        users: {
          total: totalUsers,
          active: activeUsers,
          inactive: totalUsers - activeUsers,
        },
      };
    }),
});
