import { initTR<PERSON>, TRPCError } from '@trpc/server';
import { CreateExpressContextOptions } from '@trpc/server/adapters/express';
import { ZodError } from 'zod';
import { prisma } from './database/connection';
import { verifyJWT } from './auth/jwt';
import { AuthContext, Permission } from '@fc-china/shared-types';

// Create context for tRPC
export async function createContext({ req, res }: CreateExpressContextOptions) {
  // Extract request metadata
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  const userAgent = req.headers['user-agent'] || 'unknown';
  const requestId = req.headers['x-request-id'] || generateRequestId();
  
  // Base context available to all procedures
  const baseContext = {
    req,
    res,
    db: prisma,
    ip,
    userAgent,
    requestId,
    startTime: Date.now(),
  };
  
  // Try to authenticate user from Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader?.startsWith('Bearer ')) {
    try {
      const token = authHeader.substring(7);
      const payload = await verifyJWT(token);
      
      // Fetch user details from database
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        include: {
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
              status: true,
            },
          },
        },
      });
      
      if (!user || user.status !== 'ACTIVE') {
        throw new Error('User not found or inactive');
      }
      
      const authContext: AuthContext = {
        user: {
          id: user.id,
          auth0Id: user.auth0Id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          status: user.status,
          factoryId: user.factoryId || undefined,
          permissions: user.permissions as Permission[],
          avatar: user.avatar || undefined,
        },
        token,
        isAuthenticated: true,
      };
      
      return {
        ...baseContext,
        user: authContext.user,
        auth: authContext,
        isAuthenticated: true,
      };
    } catch (error) {
      // Log authentication failure but don't throw - allow unauthenticated access to public procedures
      console.warn('Authentication failed:', error);
    }
  }
  
  return {
    ...baseContext,
    user: null,
    auth: null,
    isAuthenticated: false,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;

// Initialize tRPC
const t = initTRPC.context<Context>().create({
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError
            ? error.cause.flatten()
            : null,
      },
    };
  },
  defaultMeta: {
    authRequired: false,
  },
});

// Base router and procedure
export const router = t.router;
export const publicProcedure = t.procedure;

// Authentication middleware
const isAuthenticated = t.middleware(({ ctx, next }) => {
  if (!ctx.isAuthenticated || !ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    });
  }
  
  return next({
    ctx: {
      ...ctx,
      user: ctx.user, // Now guaranteed to be non-null
    },
  });
});

// Protected procedure (requires authentication)
export const protectedProcedure = publicProcedure.use(isAuthenticated);

// Permission-based middleware
export const requirePermission = (permission: Permission) =>
  t.middleware(({ ctx, next }) => {
    if (!ctx.isAuthenticated || !ctx.user) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'Authentication required',
      });
    }
    
    const hasPermission = ctx.user.permissions.includes(permission) || 
                         ctx.user.permissions.includes('SYSTEM_ADMIN');
    
    if (!hasPermission) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Permission required: ${permission}`,
      });
    }
    
    return next({ ctx });
  });

// Factory access middleware
export const requireFactoryAccess = t.middleware(({ ctx, next }) => {
  if (!ctx.isAuthenticated || !ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    });
  }
  
  // Super admin can access any factory
  if (ctx.user.permissions.includes('SYSTEM_ADMIN')) {
    return next({ ctx });
  }
  
  // Factory users must have a factory ID
  if (!ctx.user.factoryId) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Factory access required',
    });
  }
  
  return next({ ctx });
});

// Role-based middleware
export const requireRole = (allowedRoles: string[]) =>
  t.middleware(({ ctx, next }) => {
    if (!ctx.isAuthenticated || !ctx.user) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'Authentication required',
      });
    }
    
    if (!allowedRoles.includes(ctx.user.role)) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Role required: ${allowedRoles.join(' or ')}`,
      });
    }
    
    return next({ ctx });
  });

// Audit logging middleware
export const withAudit = (eventType: string) =>
  t.middleware(async ({ ctx, next, path, type, input }) => {
    const result = await next();
    
    // Log the operation for audit purposes
    if (ctx.user) {
      try {
        await ctx.db.auditLog.create({
          data: {
            type: 'BUSINESS',
            event: eventType,
            entityType: path.split('.')[0],
            userId: ctx.user.id,
            factoryId: ctx.user.factoryId,
            ipAddress: ctx.ip,
            userAgent: ctx.userAgent,
            metadata: {
              path,
              type,
              input: sanitizeInput(input),
              timestamp: new Date().toISOString(),
            },
          },
        });
      } catch (error) {
        console.error('Failed to create audit log:', error);
      }
    }
    
    return result;
  });

// Rate limiting middleware (basic implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export const rateLimit = (maxRequests: number, windowMs: number) =>
  t.middleware(({ ctx, next }) => {
    const key = ctx.user?.id || ctx.ip;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean up old entries
    for (const [k, v] of rateLimitMap.entries()) {
      if (v.resetTime < windowStart) {
        rateLimitMap.delete(k);
      }
    }
    
    const current = rateLimitMap.get(key);
    if (!current) {
      rateLimitMap.set(key, { count: 1, resetTime: now });
    } else if (current.resetTime < windowStart) {
      rateLimitMap.set(key, { count: 1, resetTime: now });
    } else if (current.count >= maxRequests) {
      throw new TRPCError({
        code: 'TOO_MANY_REQUESTS',
        message: 'Rate limit exceeded',
      });
    } else {
      current.count++;
    }
    
    return next();
  });

// Utility functions
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(7)}`;
}

function sanitizeInput(input: any): any {
  if (!input) return input;
  
  const sanitized = { ...input };
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  
  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}

// Export middleware combinations for common use cases
export const adminProcedure = protectedProcedure.use(requirePermission('SYSTEM_ADMIN'));
export const factoryProcedure = protectedProcedure.use(requireFactoryAccess);
export const factoryAdminProcedure = factoryProcedure.use(requirePermission('FACTORY_ADMIN'));

// Export the tRPC instance
export { t };
