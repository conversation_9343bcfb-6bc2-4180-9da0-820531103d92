import winston from 'winston';
import { config, isDevelopment, isProduction } from '../config';

// Custom log format for production
const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      service: 'fc-china-api',
      environment: config.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      ...meta,
    });
  })
);

// Development format for readability
const developmentFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  })
);

// Create logger instance
export const logger = winston.createLogger({
  level: config.LOG_LEVEL || 'info',
  format: isProduction ? productionFormat : developmentFormat,
  defaultMeta: {
    service: 'fc-china-api',
    environment: config.NODE_ENV,
  },
  transports: [
    // Console transport
    new winston.transports.Console({
      handleExceptions: true,
      handleRejections: true,
    }),
  ],
  
  // Exit on error
  exitOnError: false,
});

// Add file transports for production
if (isProduction) {
  logger.add(new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    tailable: true,
  }));
  
  logger.add(new winston.transports.File({
    filename: 'logs/combined.log',
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 10,
    tailable: true,
  }));
}

// Performance logger for slow queries and operations
export const performanceLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    ...(isProduction ? [
      new winston.transports.File({
        filename: 'logs/performance.log',
        maxsize: 10 * 1024 * 1024,
        maxFiles: 5,
      }),
    ] : []),
  ],
});

// Security logger for audit events
export const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    ...(isProduction ? [
      new winston.transports.File({
        filename: 'logs/security.log',
        maxsize: 10 * 1024 * 1024,
        maxFiles: 10,
      }),
    ] : []),
  ],
});

// Export default logger
export default logger;
