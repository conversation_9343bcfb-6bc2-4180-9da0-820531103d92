import { ManagementClient, AuthenticationClient } from 'auth0';
import { config } from '../config';
import { prisma } from '../database/connection';
import { generateJWT } from './jwt';
import { Auth0Profile, UserRole, DefaultPermissions } from '@fc-china/shared-types';

// Initialize Auth0 clients
const managementClient = new ManagementClient({
  domain: config.AUTH0_DOMAIN,
  clientId: config.AUTH0_CLIENT_ID,
  clientSecret: config.AUTH0_CLIENT_SECRET,
  scope: 'read:users update:users create:users delete:users',
});

const authenticationClient = new AuthenticationClient({
  domain: config.AUTH0_DOMAIN,
  clientId: config.AUTH0_CLIENT_ID,
  clientSecret: config.AUTH0_CLIENT_SECRET,
});

// Sync Auth0 user with our database
export async function syncAuth0User(auth0Profile: Auth0Profile): Promise<{
  user: any;
  token: string;
  isNewUser: boolean;
}> {
  try {
    // Check if user exists in our database
    let user = await prisma.user.findUnique({
      where: { auth0Id: auth0Profile.sub },
      include: {
        factory: {
          select: {
            id: true,
            name: true,
            slug: true,
            status: true,
          },
        },
      },
    });
    
    let isNewUser = false;
    
    if (!user) {
      // Create new user
      isNewUser = true;
      
      // Determine default role based on email domain or other criteria
      const defaultRole: UserRole = determineDefaultRole(auth0Profile.email);
      
      user = await prisma.user.create({
        data: {
          auth0Id: auth0Profile.sub,
          email: auth0Profile.email,
          firstName: auth0Profile.given_name || auth0Profile.name?.split(' ')[0] || 'User',
          lastName: auth0Profile.family_name || auth0Profile.name?.split(' ').slice(1).join(' ') || '',
          avatar: auth0Profile.picture,
          role: defaultRole,
          status: 'ACTIVE',
          permissions: DefaultPermissions[defaultRole],
          language: auth0Profile.locale || 'en',
        },
        include: {
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
              status: true,
            },
          },
        },
      });
      
      // Update Auth0 user metadata
      await updateAuth0UserMetadata(auth0Profile.sub, {
        userId: user.id,
        role: user.role,
        factoryId: user.factoryId,
      });
    } else {
      // Update existing user with latest Auth0 data
      user = await prisma.user.update({
        where: { id: user.id },
        data: {
          email: auth0Profile.email,
          firstName: auth0Profile.given_name || user.firstName,
          lastName: auth0Profile.family_name || user.lastName,
          avatar: auth0Profile.picture || user.avatar,
          lastLoginAt: new Date(),
        },
        include: {
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
              status: true,
            },
          },
        },
      });
    }
    
    // Generate JWT token
    const token = generateJWT({
      userId: user.id,
      auth0Id: user.auth0Id,
      email: user.email,
      role: user.role,
      factoryId: user.factoryId || undefined,
      permissions: user.permissions as any[],
    });
    
    return { user, token, isNewUser };
  } catch (error) {
    console.error('Error syncing Auth0 user:', error);
    throw new Error('Failed to sync user data');
  }
}

// Update Auth0 user metadata
export async function updateAuth0UserMetadata(
  auth0Id: string,
  metadata: {
    userId?: string;
    role?: UserRole;
    factoryId?: string | null;
    permissions?: string[];
  }
): Promise<void> {
  try {
    await managementClient.updateUser(
      { id: auth0Id },
      {
        app_metadata: {
          ...metadata,
          updated_at: new Date().toISOString(),
        },
      }
    );
  } catch (error) {
    console.error('Error updating Auth0 user metadata:', error);
    // Don't throw - this is not critical for user flow
  }
}

// Get Auth0 user by ID
export async function getAuth0User(auth0Id: string): Promise<Auth0Profile | null> {
  try {
    const user = await managementClient.getUser({ id: auth0Id });
    return user as Auth0Profile;
  } catch (error) {
    console.error('Error fetching Auth0 user:', error);
    return null;
  }
}

// Create Auth0 user (for admin user creation)
export async function createAuth0User(userData: {
  email: string;
  password?: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  factoryId?: string;
}): Promise<string> {
  try {
    const auth0User = await managementClient.createUser({
      connection: 'Username-Password-Authentication',
      email: userData.email,
      password: userData.password || generateTemporaryPassword(),
      given_name: userData.firstName,
      family_name: userData.lastName,
      name: `${userData.firstName} ${userData.lastName}`,
      app_metadata: {
        role: userData.role,
        factoryId: userData.factoryId,
        permissions: DefaultPermissions[userData.role],
      },
      email_verified: false,
    });
    
    if (!auth0User.user_id) {
      throw new Error('Failed to create Auth0 user');
    }
    
    return auth0User.user_id;
  } catch (error) {
    console.error('Error creating Auth0 user:', error);
    throw new Error('Failed to create user account');
  }
}

// Delete Auth0 user
export async function deleteAuth0User(auth0Id: string): Promise<void> {
  try {
    await managementClient.deleteUser({ id: auth0Id });
  } catch (error) {
    console.error('Error deleting Auth0 user:', error);
    throw new Error('Failed to delete user account');
  }
}

// Send password reset email
export async function sendPasswordResetEmail(email: string): Promise<void> {
  try {
    await authenticationClient.requestChangePasswordEmail({
      email,
      connection: 'Username-Password-Authentication',
    });
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw new Error('Failed to send password reset email');
  }
}

// Send email verification
export async function sendEmailVerification(auth0Id: string): Promise<void> {
  try {
    await managementClient.sendEmailVerification({ user_id: auth0Id });
  } catch (error) {
    console.error('Error sending email verification:', error);
    throw new Error('Failed to send email verification');
  }
}

// Block/unblock user
export async function blockAuth0User(auth0Id: string, blocked: boolean): Promise<void> {
  try {
    await managementClient.updateUser({ id: auth0Id }, { blocked });
  } catch (error) {
    console.error('Error updating user block status:', error);
    throw new Error('Failed to update user status');
  }
}

// Get user roles from Auth0
export async function getAuth0UserRoles(auth0Id: string): Promise<string[]> {
  try {
    const roles = await managementClient.getUserRoles({ id: auth0Id });
    return roles.map(role => role.name || '');
  } catch (error) {
    console.error('Error fetching user roles:', error);
    return [];
  }
}

// Assign role to user in Auth0
export async function assignAuth0UserRole(auth0Id: string, roleId: string): Promise<void> {
  try {
    await managementClient.assignRolestoUser({ id: auth0Id }, { roles: [roleId] });
  } catch (error) {
    console.error('Error assigning user role:', error);
    throw new Error('Failed to assign user role');
  }
}

// Helper functions
function determineDefaultRole(email: string): UserRole {
  // You can implement custom logic here based on email domain, etc.
  // For now, default to CUSTOMER
  return 'CUSTOMER';
}

function generateTemporaryPassword(): string {
  // Generate a secure temporary password
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

// Validate Auth0 token (for webhook verification)
export async function validateAuth0Token(token: string): Promise<boolean> {
  try {
    // This would typically validate the token signature
    // Implementation depends on your Auth0 setup
    return true;
  } catch {
    return false;
  }
}

// Auth0 webhook handler for user events
export async function handleAuth0Webhook(event: {
  type: string;
  data: any;
}): Promise<void> {
  try {
    switch (event.type) {
      case 'user.created':
        await handleUserCreated(event.data);
        break;
      case 'user.updated':
        await handleUserUpdated(event.data);
        break;
      case 'user.deleted':
        await handleUserDeleted(event.data);
        break;
      default:
        console.log('Unhandled Auth0 webhook event:', event.type);
    }
  } catch (error) {
    console.error('Error handling Auth0 webhook:', error);
    throw error;
  }
}

async function handleUserCreated(userData: any): Promise<void> {
  // Handle user creation from Auth0
  console.log('User created in Auth0:', userData.user_id);
}

async function handleUserUpdated(userData: any): Promise<void> {
  // Handle user updates from Auth0
  console.log('User updated in Auth0:', userData.user_id);
}

async function handleUserDeleted(userData: any): Promise<void> {
  // Handle user deletion from Auth0
  try {
    await prisma.user.update({
      where: { auth0Id: userData.user_id },
      data: { status: 'INACTIVE' },
    });
  } catch (error) {
    console.error('Error handling user deletion:', error);
  }
}
