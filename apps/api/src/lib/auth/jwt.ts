import jwt from 'jsonwebtoken';
import { config } from '../config';
import { JWTPayload, UserRole, Permission } from '@fc-china/shared-types';

// JWT token generation
export function generateJWT(payload: {
  userId: string;
  auth0Id: string;
  email: string;
  role: UserRole;
  factoryId?: string;
  permissions: Permission[];
}): string {
  const jwtPayload: Omit<JWTPayload, 'iat' | 'exp' | 'aud' | 'iss'> = {
    sub: payload.auth0Id,
    userId: payload.userId,
    email: payload.email,
    role: payload.role,
    factoryId: payload.factoryId,
    permissions: payload.permissions,
  };
  
  return jwt.sign(jwtPayload, config.JWT_SECRET, {
    expiresIn: config.JWT_EXPIRES_IN,
    audience: config.AUTH0_AUDIENCE,
    issuer: config.AUTH0_DOMAIN,
    algorithm: 'HS256',
  });
}

// JWT token verification
export async function verifyJWT(token: string): Promise<JWTPayload> {
  try {
    const decoded = jwt.verify(token, config.JWT_SECRET, {
      audience: config.AUTH0_AUDIENCE,
      issuer: config.AUTH0_DOMAIN,
      algorithms: ['HS256'],
    }) as JWTPayload;
    
    // Validate required fields
    if (!decoded.userId || !decoded.email || !decoded.role) {
      throw new Error('Invalid token payload');
    }
    
    return decoded;
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error(`Invalid token: ${error.message}`);
    }
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Token expired');
    }
    if (error instanceof jwt.NotBeforeError) {
      throw new Error('Token not active');
    }
    throw error;
  }
}

// Refresh token generation (longer expiry)
export function generateRefreshToken(userId: string): string {
  return jwt.sign(
    { userId, type: 'refresh' },
    config.JWT_SECRET,
    {
      expiresIn: '7d', // 7 days
      audience: config.AUTH0_AUDIENCE,
      issuer: config.AUTH0_DOMAIN,
      algorithm: 'HS256',
    }
  );
}

// Refresh token verification
export async function verifyRefreshToken(token: string): Promise<{ userId: string }> {
  try {
    const decoded = jwt.verify(token, config.JWT_SECRET, {
      audience: config.AUTH0_AUDIENCE,
      issuer: config.AUTH0_DOMAIN,
      algorithms: ['HS256'],
    }) as any;
    
    if (decoded.type !== 'refresh' || !decoded.userId) {
      throw new Error('Invalid refresh token');
    }
    
    return { userId: decoded.userId };
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error(`Invalid refresh token: ${error.message}`);
    }
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Refresh token expired');
    }
    throw error;
  }
}

// Extract token from Authorization header
export function extractTokenFromHeader(authHeader?: string): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Token blacklist (in-memory for now, should use Redis in production)
const tokenBlacklist = new Set<string>();

// Add token to blacklist
export function blacklistToken(token: string): void {
  tokenBlacklist.add(token);
}

// Check if token is blacklisted
export function isTokenBlacklisted(token: string): boolean {
  return tokenBlacklist.has(token);
}

// Decode token without verification (for debugging)
export function decodeJWT(token: string): JWTPayload | null {
  try {
    return jwt.decode(token) as JWTPayload;
  } catch {
    return null;
  }
}

// Get token expiration time
export function getTokenExpiration(token: string): Date | null {
  const decoded = decodeJWT(token);
  if (!decoded?.exp) return null;
  return new Date(decoded.exp * 1000);
}

// Check if token is expired
export function isTokenExpired(token: string): boolean {
  const expiration = getTokenExpiration(token);
  if (!expiration) return true;
  return expiration < new Date();
}

// Get time until token expires
export function getTokenTimeToExpiry(token: string): number | null {
  const expiration = getTokenExpiration(token);
  if (!expiration) return null;
  return Math.max(0, expiration.getTime() - Date.now());
}

// Validate token format
export function isValidTokenFormat(token: string): boolean {
  if (!token || typeof token !== 'string') return false;
  
  // JWT tokens have 3 parts separated by dots
  const parts = token.split('.');
  if (parts.length !== 3) return false;
  
  // Each part should be base64url encoded
  try {
    parts.forEach(part => {
      if (!part) throw new Error('Empty part');
      // Basic base64url validation
      if (!/^[A-Za-z0-9_-]+$/.test(part)) {
        throw new Error('Invalid base64url');
      }
    });
    return true;
  } catch {
    return false;
  }
}

// Create API key (for service-to-service communication)
export function generateAPIKey(serviceId: string, permissions: Permission[]): string {
  return jwt.sign(
    {
      serviceId,
      permissions,
      type: 'api_key',
    },
    config.JWT_SECRET,
    {
      expiresIn: '1y', // 1 year
      audience: config.AUTH0_AUDIENCE,
      issuer: config.AUTH0_DOMAIN,
      algorithm: 'HS256',
    }
  );
}

// Verify API key
export async function verifyAPIKey(token: string): Promise<{
  serviceId: string;
  permissions: Permission[];
}> {
  try {
    const decoded = jwt.verify(token, config.JWT_SECRET, {
      audience: config.AUTH0_AUDIENCE,
      issuer: config.AUTH0_DOMAIN,
      algorithms: ['HS256'],
    }) as any;
    
    if (decoded.type !== 'api_key' || !decoded.serviceId) {
      throw new Error('Invalid API key');
    }
    
    return {
      serviceId: decoded.serviceId,
      permissions: decoded.permissions || [],
    };
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new Error(`Invalid API key: ${error.message}`);
    }
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('API key expired');
    }
    throw error;
  }
}
