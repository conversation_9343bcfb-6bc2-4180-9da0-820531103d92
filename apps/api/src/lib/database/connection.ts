import { PrismaClient } from '@prisma/client';
import { config, isDevelopment } from '../config';

// Prisma Client configuration
const prismaConfig = {
  datasources: {
    db: {
      url: config.DATABASE_URL,
    },
  },
  log: isDevelopment 
    ? ['query', 'info', 'warn', 'error'] as const
    : ['warn', 'error'] as const,
};

// Global Prisma instance for development (prevents multiple instances)
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Create Prisma client instance
export const prisma = globalForPrisma.prisma ?? new PrismaClient(prismaConfig);

// In development, store the instance globally to prevent multiple connections
if (isDevelopment) {
  globalForPrisma.prisma = prisma;
}

// Database health check function
export async function checkDatabaseHealth(): Promise<{
  healthy: boolean;
  latency: number;
  activeConnections?: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    // Simple query to check database connectivity
    await prisma.$queryRaw`SELECT 1`;
    
    const latency = Date.now() - startTime;
    
    // Get connection pool info (if available)
    let activeConnections: number | undefined;
    try {
      const result = await prisma.$queryRaw<Array<{ count: bigint }>>`
        SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'
      `;
      activeConnections = Number(result[0]?.count || 0);
    } catch {
      // Ignore if we can't get connection info
    }
    
    return {
      healthy: true,
      latency,
      activeConnections,
    };
  } catch (error) {
    return {
      healthy: false,
      latency: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown database error',
    };
  }
}

// Database connection event handlers
prisma.$on('beforeExit', async () => {
  console.log('🔌 Disconnecting from database...');
});

// Graceful shutdown handler
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log('✅ Database disconnected successfully');
  } catch (error) {
    console.error('❌ Error disconnecting from database:', error);
  }
}

// Database initialization function
export async function initializeDatabase(): Promise<void> {
  try {
    // Test the connection
    await prisma.$connect();
    console.log('✅ Database connected successfully');
    
    // Run health check
    const health = await checkDatabaseHealth();
    if (health.healthy) {
      console.log(`📊 Database health check passed (${health.latency}ms)`);
      if (health.activeConnections !== undefined) {
        console.log(`🔗 Active connections: ${health.activeConnections}`);
      }
    } else {
      console.warn('⚠️ Database health check failed:', health.error);
    }
  } catch (error) {
    console.error('❌ Failed to connect to database:', error);
    throw error;
  }
}

// Middleware for request-scoped database transactions
export function withTransaction<T>(
  callback: (tx: PrismaClient) => Promise<T>
): Promise<T> {
  return prisma.$transaction(callback);
}

// Database query performance monitoring
export function withQueryLogging<T>(
  operation: string,
  callback: () => Promise<T>
): Promise<T> {
  const startTime = Date.now();
  
  return callback()
    .then((result) => {
      const duration = Date.now() - startTime;
      if (duration > 1000) {
        console.warn(`🐌 Slow query detected: ${operation} took ${duration}ms`);
      } else if (isDevelopment) {
        console.log(`⚡ Query completed: ${operation} (${duration}ms)`);
      }
      return result;
    })
    .catch((error) => {
      const duration = Date.now() - startTime;
      console.error(`❌ Query failed: ${operation} (${duration}ms)`, error);
      throw error;
    });
}

// Export Prisma client as default
export default prisma;
