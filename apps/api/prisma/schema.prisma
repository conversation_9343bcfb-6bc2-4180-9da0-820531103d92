// FC-CHINA Production Database Schema
// This schema is optimized for multi-tenant B2B manufacturing platform

generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/client"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// ============================================================================
// AUTHENTICATION & USER MANAGEMENT
// ============================================================================

model User {
  id          String   @id @default(cuid())
  auth0Id     String   @unique @map("auth0_id")
  email       String   @unique
  firstName   String   @map("first_name")
  lastName    String   @map("last_name")
  avatar      String?
  phone       String?
  language    String   @default("en")
  timezone    String   @default("UTC")
  status      UserStatus @default(ACTIVE)
  role        UserRole
  
  // Multi-tenant relationship
  factoryId   String?  @map("factory_id")
  factory     Factory? @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Permissions
  permissions String[] @default([])
  
  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  lastLoginAt DateTime? @map("last_login_at")
  
  // Relations
  createdProducts    Product[] @relation("ProductCreatedBy")
  updatedProducts    Product[] @relation("ProductUpdatedBy")
  createdOrders      Order[]   @relation("OrderCreatedBy")
  assignedOrders     Order[]   @relation("OrderAssignedTo")
  sentMessages       Message[] @relation("MessageSender")
  receivedMessages   Message[] @relation("MessageReceiver")
  auditLogs          AuditLog[]
  notifications      Notification[]
  
  @@map("users")
  @@index([auth0Id])
  @@index([email])
  @@index([factoryId])
  @@index([status])
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

enum UserRole {
  SUPER_ADMIN
  FACTORY_OWNER
  FACTORY_ADMIN
  FACTORY_MANAGER
  FACTORY_STAFF
  CUSTOMER
  CUSTOMER_ADMIN
}

// ============================================================================
// FACTORY MANAGEMENT
// ============================================================================

model Factory {
  id                String   @id @default(cuid())
  name              String
  slug              String   @unique
  description       String?
  logo              String?
  coverImage        String?  @map("cover_image")
  
  // Business information
  businessLicense   String?  @map("business_license")
  taxId             String?  @map("tax_id")
  establishedYear   Int?     @map("established_year")
  employeeCount     Int?     @map("employee_count")
  annualRevenue     Decimal? @map("annual_revenue") @db.Decimal(15, 2)
  
  // Contact information
  email             String
  phone             String?
  website           String?
  
  // Address
  addressStreet     String   @map("address_street")
  addressCity       String   @map("address_city")
  addressState      String   @map("address_state")
  addressPostalCode String   @map("address_postal_code")
  addressCountry    String   @map("address_country")
  latitude          Float?
  longitude         Float?
  
  // Business settings
  currency          Currency @default(USD)
  timezone          String   @default("UTC")
  language          String   @default("en")
  
  // Verification and status
  verificationStatus VerificationStatus @default(UNVERIFIED) @map("verification_status")
  status            FactoryStatus @default(ACTIVE)
  
  // Subscription
  subscriptionTier  SubscriptionTier @default(FREE) @map("subscription_tier")
  subscriptionEndsAt DateTime? @map("subscription_ends_at")
  
  // Audit fields
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  
  // Relations
  users             User[]
  products          Product[]
  categories        Category[]
  orders            Order[]
  inquiries         Inquiry[]
  messages          Message[]
  
  @@map("factories")
  @@index([slug])
  @@index([status])
  @@index([verificationStatus])
  @@index([subscriptionTier])
  @@index([addressCountry])
}

enum FactoryStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_APPROVAL
}

enum VerificationStatus {
  UNVERIFIED
  PENDING
  VERIFIED
  REJECTED
}

enum SubscriptionTier {
  FREE
  BASIC
  STANDARD
  PREMIUM
  ENTERPRISE
}

enum Currency {
  USD
  EUR
  GBP
  CNY
  JPY
  KRW
  CAD
  AUD
}

// ============================================================================
// PRODUCT MANAGEMENT
// ============================================================================

model Category {
  id          String   @id @default(cuid())
  name        String
  slug        String
  description String?
  image       String?
  parentId    String?  @map("parent_id")
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  
  // Multi-tenant
  factoryId   String   @map("factory_id")
  factory     Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Metadata
  sortOrder   Int      @default(0) @map("sort_order")
  isActive    Boolean  @default(true) @map("is_active")
  
  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  // Relations
  products    Product[]
  
  @@map("categories")
  @@unique([factoryId, slug])
  @@index([factoryId])
  @@index([parentId])
  @@index([isActive])
}

model Product {
  id              String   @id @default(cuid())
  name            String
  slug            String
  description     String?
  shortDescription String? @map("short_description")
  
  // Pricing
  basePrice       Decimal  @map("base_price") @db.Decimal(10, 2)
  currency        Currency @default(USD)
  minOrderQty     Int      @default(1) @map("min_order_qty")
  maxOrderQty     Int?     @map("max_order_qty")
  
  // Product details
  sku             String?
  model           String?
  brand           String?
  weight          Decimal? @db.Decimal(8, 3)
  dimensions      Json?    // {length, width, height, unit}
  materials       String[] @default([])
  colors          String[] @default([])
  tags            String[] @default([])
  
  // Inventory
  stockQuantity   Int      @default(0) @map("stock_quantity")
  stockStatus     StockStatus @default(IN_STOCK) @map("stock_status")
  
  // SEO and metadata
  metaTitle       String?  @map("meta_title")
  metaDescription String?  @map("meta_description")
  
  // Status and visibility
  status          ProductStatus @default(DRAFT)
  isActive        Boolean  @default(true) @map("is_active")
  isFeatured      Boolean  @default(false) @map("is_featured")
  
  // Multi-tenant
  factoryId       String   @map("factory_id")
  factory         Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)
  
  // Category
  categoryId      String   @map("category_id")
  category        Category @relation(fields: [categoryId], references: [id])
  
  // Audit fields
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  createdBy       String   @map("created_by")
  updatedBy       String   @map("updated_by")
  createdByUser   User     @relation("ProductCreatedBy", fields: [createdBy], references: [id])
  updatedByUser   User     @relation("ProductUpdatedBy", fields: [updatedBy], references: [id])
  
  // Relations
  images          ProductImage[]
  variants        ProductVariant[]
  orderItems      OrderItem[]
  inquiries       Inquiry[]
  reviews         ProductReview[]
  
  @@map("products")
  @@unique([factoryId, slug])
  @@index([factoryId])
  @@index([categoryId])
  @@index([status])
  @@index([isActive])
  @@index([isFeatured])
  @@index([stockStatus])
  @@index([createdAt])
}

enum ProductStatus {
  DRAFT
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
  DISCONTINUED
}

enum StockStatus {
  IN_STOCK
  LOW_STOCK
  OUT_OF_STOCK
  BACKORDER
}

model ProductImage {
  id        String   @id @default(cuid())
  url       String
  alt       String?
  caption   String?
  sortOrder Int      @default(0) @map("sort_order")
  isMain    Boolean  @default(false) @map("is_main")
  
  // Product relationship
  productId String   @map("product_id")
  product   Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  // Audit fields
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  @@map("product_images")
  @@index([productId])
  @@index([isMain])
}

model ProductVariant {
  id          String   @id @default(cuid())
  name        String
  sku         String?
  price       Decimal  @db.Decimal(10, 2)
  
  // Variant attributes
  attributes  Json     // {color: "red", size: "large", etc.}
  
  // Inventory
  stockQuantity Int    @default(0) @map("stock_quantity")
  stockStatus   StockStatus @default(IN_STOCK) @map("stock_status")
  
  // Status
  isActive    Boolean  @default(true) @map("is_active")
  
  // Product relationship
  productId   String   @map("product_id")
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  // Relations
  images      ProductImage[]
  orderItems  OrderItem[]
  
  @@map("product_variants")
  @@unique([productId, sku])
  @@index([productId])
  @@index([isActive])
}

// ============================================================================
// ORDER MANAGEMENT
// ============================================================================

model Order {
  id              String   @id @default(cuid())
  orderNumber     String   @unique @map("order_number")

  // Order details
  status          OrderStatus @default(PENDING)
  totalAmount     Decimal  @map("total_amount") @db.Decimal(12, 2)
  currency        Currency @default(USD)

  // Customer information
  customerName    String   @map("customer_name")
  customerEmail   String   @map("customer_email")
  customerPhone   String?  @map("customer_phone")
  customerCompany String?  @map("customer_company")

  // Shipping address
  shippingStreet     String   @map("shipping_street")
  shippingCity       String   @map("shipping_city")
  shippingState      String   @map("shipping_state")
  shippingPostalCode String   @map("shipping_postal_code")
  shippingCountry    String   @map("shipping_country")

  // Billing address (optional, defaults to shipping)
  billingStreet      String?  @map("billing_street")
  billingCity        String?  @map("billing_city")
  billingState       String?  @map("billing_state")
  billingPostalCode  String?  @map("billing_postal_code")
  billingCountry     String?  @map("billing_country")

  // Order metadata
  notes           String?
  internalNotes   String?  @map("internal_notes")

  // Dates
  orderDate       DateTime @default(now()) @map("order_date")
  requiredDate    DateTime? @map("required_date")
  shippedDate     DateTime? @map("shipped_date")
  deliveredDate   DateTime? @map("delivered_date")

  // Multi-tenant
  factoryId       String   @map("factory_id")
  factory         Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  // Assignment
  assignedToId    String?  @map("assigned_to_id")
  assignedTo      User?    @relation("OrderAssignedTo", fields: [assignedToId], references: [id])

  // Audit fields
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  createdBy       String   @map("created_by")
  createdByUser   User     @relation("OrderCreatedBy", fields: [createdBy], references: [id])

  // Relations
  items           OrderItem[]
  messages        Message[]
  statusHistory   OrderStatusHistory[]

  @@map("orders")
  @@index([factoryId])
  @@index([status])
  @@index([orderDate])
  @@index([customerEmail])
}

enum OrderStatus {
  PENDING
  CONFIRMED
  IN_PRODUCTION
  READY_TO_SHIP
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

model OrderItem {
  id            String   @id @default(cuid())
  quantity      Int
  unitPrice     Decimal  @map("unit_price") @db.Decimal(10, 2)
  totalPrice    Decimal  @map("total_price") @db.Decimal(12, 2)

  // Product relationship
  productId     String   @map("product_id")
  product       Product  @relation(fields: [productId], references: [id])

  // Variant relationship (optional)
  variantId     String?  @map("variant_id")
  variant       ProductVariant? @relation(fields: [variantId], references: [id])

  // Order relationship
  orderId       String   @map("order_id")
  order         Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  // Item specifications
  specifications Json?   // Custom specifications for this order item

  // Audit fields
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("order_items")
  @@index([orderId])
  @@index([productId])
}

model OrderStatusHistory {
  id        String      @id @default(cuid())
  status    OrderStatus
  notes     String?

  // Order relationship
  orderId   String      @map("order_id")
  order     Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt DateTime    @default(now()) @map("created_at")
  createdBy String      @map("created_by")

  @@map("order_status_history")
  @@index([orderId])
  @@index([createdAt])
}

// ============================================================================
// COMMUNICATION & MESSAGING
// ============================================================================

model Inquiry {
  id            String   @id @default(cuid())
  subject       String
  message       String
  status        InquiryStatus @default(OPEN)

  // Customer information
  customerName  String   @map("customer_name")
  customerEmail String   @map("customer_email")
  customerPhone String?  @map("customer_phone")
  customerCompany String? @map("customer_company")

  // Product relationship (optional)
  productId     String?  @map("product_id")
  product       Product? @relation(fields: [productId], references: [id])

  // Factory relationship
  factoryId     String   @map("factory_id")
  factory       Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  messages      Message[]

  @@map("inquiries")
  @@index([factoryId])
  @@index([status])
  @@index([customerEmail])
  @@index([createdAt])
}

enum InquiryStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

model Message {
  id          String   @id @default(cuid())
  content     String
  messageType MessageType @default(TEXT) @map("message_type")

  // Sender and receiver
  senderId    String   @map("sender_id")
  sender      User     @relation("MessageSender", fields: [senderId], references: [id])
  receiverId  String?  @map("receiver_id")
  receiver    User?    @relation("MessageReceiver", fields: [receiverId], references: [id])

  // Context relationships (one of these will be set)
  orderId     String?  @map("order_id")
  order       Order?   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  inquiryId   String?  @map("inquiry_id")
  inquiry     Inquiry? @relation(fields: [inquiryId], references: [id], onDelete: Cascade)

  // Factory context
  factoryId   String   @map("factory_id")
  factory     Factory  @relation(fields: [factoryId], references: [id], onDelete: Cascade)

  // Message metadata
  isRead      Boolean  @default(false) @map("is_read")
  readAt      DateTime? @map("read_at")
  attachments Json?    // Array of file URLs

  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("messages")
  @@index([senderId])
  @@index([receiverId])
  @@index([orderId])
  @@index([inquiryId])
  @@index([factoryId])
  @@index([createdAt])
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  SYSTEM
}

// ============================================================================
// REVIEWS & RATINGS
// ============================================================================

model ProductReview {
  id          String   @id @default(cuid())
  rating      Int      // 1-5 stars
  title       String?
  comment     String?

  // Reviewer information
  reviewerName  String @map("reviewer_name")
  reviewerEmail String @map("reviewer_email")
  reviewerCompany String? @map("reviewer_company")

  // Product relationship
  productId   String   @map("product_id")
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Status
  isApproved  Boolean  @default(false) @map("is_approved")
  isVisible   Boolean  @default(true) @map("is_visible")

  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("product_reviews")
  @@index([productId])
  @@index([rating])
  @@index([isApproved])
  @@index([createdAt])
}

// ============================================================================
// SYSTEM & AUDIT
// ============================================================================

model AuditLog {
  id          String   @id @default(cuid())
  type        AuditType
  event       String
  entityType  String?  @map("entity_type")
  entityId    String?  @map("entity_id")

  // User context
  userId      String?  @map("user_id")
  user        User?    @relation(fields: [userId], references: [id])

  // Factory context
  factoryId   String?  @map("factory_id")

  // Request context
  ipAddress   String?  @map("ip_address")
  userAgent   String?  @map("user_agent")

  // Event data
  metadata    Json?

  // Audit fields
  timestamp   DateTime @default(now())

  @@map("audit_logs")
  @@index([type])
  @@index([userId])
  @@index([factoryId])
  @@index([timestamp])
  @@index([entityType, entityId])
}

enum AuditType {
  SECURITY
  BUSINESS
  SYSTEM
}

model Notification {
  id          String   @id @default(cuid())
  type        NotificationType
  title       String
  message     String
  isRead      Boolean  @default(false) @map("is_read")
  readAt      DateTime? @map("read_at")

  // User relationship
  userId      String   @map("user_id")
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Notification data
  data        Json?

  // Audit fields
  createdAt   DateTime @default(now()) @map("created_at")

  @@map("notifications")
  @@index([userId])
  @@index([type])
  @@index([isRead])
  @@index([createdAt])
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  ORDER_UPDATE
  MESSAGE
  SYSTEM
}
