# FC-CHINA Environment Configuration
# Copy this file to .env.local and fill in your values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL="postgresql://username:password@localhost:5432/fc_china_dev"
DIRECT_URL="postgresql://username:password@localhost:5432/fc_china_dev"

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
SUPABASE_URL="https://ejrxrhojmrjpjodogtxq.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqcnhyaG9qbXJqcGpvZG9ndHhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDcwNTcsImV4cCI6MjA2ODU4MzA1N30.mqyGNK_0N0oawTGAyNQxAoH7nW8jrLRoSKmOTmySXAc"
SUPABASE_SERVICE_KEY="your-service-key-from-supabase-dashboard"

# =============================================================================
# AUTH0 CONFIGURATION
# =============================================================================
AUTH0_DOMAIN="your-domain.auth0.com"
AUTH0_CLIENT_ID="your-client-id"
AUTH0_CLIENT_SECRET="your-client-secret"
AUTH0_AUDIENCE="https://api.fc-china.com"
AUTH0_SCOPE="openid profile email"

# =============================================================================
# NEXTJS CONFIGURATION
# =============================================================================
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_URL="http://localhost:3001"
NEXT_PUBLIC_API_URL="http://localhost:3001"

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE="10485760" # 10MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp,application/pdf"

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
SENTRY_DSN="https://<EMAIL>/project-id"
DATADOG_API_KEY="your-datadog-api-key"
GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"

# =============================================================================
# PAYMENT PROVIDERS
# =============================================================================
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
NODE_ENV="development"
LOG_LEVEL="debug"
ENABLE_QUERY_LOGGING="true"
ENABLE_PRISMA_STUDIO="true"

# =============================================================================
# SECURITY
# =============================================================================
JWT_SECRET="your-jwt-secret-key"
ENCRYPTION_KEY="your-32-character-encryption-key"
CORS_ORIGIN="http://localhost:3000,http://localhost:3001"

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_ANALYTICS="true"
ENABLE_REAL_TIME_MESSAGING="true"
ENABLE_PAYMENT_PROCESSING="false"
ENABLE_MOBILE_PUSH_NOTIFICATIONS="false"

# =============================================================================
# MOBILE APP CONFIGURATION
# =============================================================================
MOBILE_API_URL="http://localhost:3001"
PUSH_NOTIFICATION_KEY="your-fcm-server-key"
APPLE_PUSH_CERT_PATH="./certs/apple-push-cert.p8"
APPLE_PUSH_KEY_ID="your-apple-key-id"
APPLE_TEAM_ID="your-apple-team-id"

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
ALIBABA_API_KEY="your-alibaba-api-key"
WISE_API_KEY="your-wise-api-key"
COINBASE_API_KEY="your-coinbase-api-key"

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
TEST_DATABASE_URL="postgresql://username:password@localhost:5432/fc_china_test"
TEST_REDIS_URL="redis://localhost:6379/1"
CYPRESS_BASE_URL="http://localhost:3000"
