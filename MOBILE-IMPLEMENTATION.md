# FC-CHINA Mobile Implementation Guide
## 📱 **Flutter Mobile Architecture (Android & iOS Only)**

**Platform Scope**: This guide covers Flutter development exclusively for mobile platforms:
- ✅ **Android**: Smartphones and tablets
- ✅ **iOS**: iPhones and iPads
- ❌ **Desktop platforms excluded**: No Windows, macOS, or Linux desktop apps

### **Mobile Project Structure (Android & iOS)**
```
apps/mobile/
├── lib/
│   ├── main_factory.dart          # Factory mobile app entry point
│   ├── main_customer.dart         # Customer mobile app entry point
│   ├── shared/                    # Shared code between mobile flavors
│   │   ├── models/               # Data models
│   │   ├── services/             # API and business services
│   │   ├── providers/            # Riverpod providers
│   │   ├── widgets/              # Reusable mobile widgets
│   │   ├── utils/                # Mobile utility functions
│   │   └── constants/            # Mobile app constants
│   ├── factory/                   # Factory mobile app code
│   │   ├── screens/              # Factory mobile screens
│   │   ├── widgets/              # Factory mobile widgets
│   │   └── providers/            # Factory mobile providers
│   ├── customer/                  # Customer mobile app code
│   │   ├── screens/              # Customer mobile screens
│   │   ├── widgets/              # Customer mobile widgets
│   │   └── providers/            # Customer mobile providers
│   └── config/                    # Mobile configuration
│       ├── app_config.dart       # Mobile app configuration
│       ├── api_config.dart       # API configuration
│       └── theme_config.dart     # Mobile theme configuration
├── android/                       # Android mobile platform
│   ├── app/
│   │   ├── src/
│   │   │   ├── factory/          # Factory Android flavor
│   │   │   ├── customer/         # Customer Android flavor
│   │   │   └── main/             # Shared Android mobile code
│   │   └── build.gradle
│   └── build.gradle
├── ios/                           # iOS mobile platform
│   ├── Factory/                   # Factory iOS target
│   ├── Customer/                  # Customer iOS target
│   └── Runner/                    # Shared iOS mobile code
└── test/
    ├── unit/                      # Mobile unit tests
    ├── widget/                    # Mobile widget tests
    └── integration/               # Mobile integration tests
```

### **Flavor Configuration**
```dart
// lib/config/app_config.dart
enum AppFlavor { factory, customer }

class AppConfig {
  static AppFlavor? _flavor;
  
  static AppFlavor get flavor => _flavor ?? AppFlavor.customer;
  
  static void setFlavor(AppFlavor flavor) {
    _flavor = flavor;
  }
  
  static String get appName {
    switch (flavor) {
      case AppFlavor.factory:
        return 'FC-CHINA Factory Mobile';
      case AppFlavor.customer:
        return 'FC-CHINA Mobile';
    }
  }

  static String get appId {
    switch (flavor) {
      case AppFlavor.factory:
        return 'com.fcchina.factory.mobile';
      case AppFlavor.customer:
        return 'com.fcchina.customer.mobile';
    }
  }
  
  static String get apiBaseUrl {
    const environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'dev');
    switch (environment) {
      case 'prod':
        return 'https://api.fc-china.com';
      case 'staging':
        return 'https://api-staging.fc-china.com';
      default:
        return 'http://localhost:3001';
    }
  }
  
  static Color get primaryColor {
    switch (flavor) {
      case AppFlavor.factory:
        return const Color(0xFF1E40AF); // Blue
      case AppFlavor.customer:
        return const Color(0xFF3B82F6); // Lighter blue
    }
  }
}
```

---

## 🔐 **Authentication Implementation**

### **Auth0 Integration**
```dart
// lib/shared/services/auth_service.dart
import 'package:auth0_flutter/auth0_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class AuthService {
  static const _storage = FlutterSecureStorage();
  late final Auth0 _auth0;
  
  AuthService() {
    _auth0 = Auth0(
      domain: const String.fromEnvironment('AUTH0_DOMAIN'),
      clientId: const String.fromEnvironment('AUTH0_CLIENT_ID'),
    );
  }
  
  Future<UserProfile?> login() async {
    try {
      final credentials = await _auth0.webAuthentication(
        scheme: AppConfig.appId,
      ).login(
        audience: 'https://api.fc-china.com',
        scopes: {'openid', 'profile', 'email', 'offline_access'},
        parameters: {
          'flavor': AppConfig.flavor.name,
        },
      );
      
      // Store tokens securely
      await _storage.write(key: 'access_token', value: credentials.accessToken);
      await _storage.write(key: 'refresh_token', value: credentials.refreshToken);
      await _storage.write(key: 'id_token', value: credentials.idToken);
      
      return credentials.user;
    } catch (e) {
      throw AuthException('Login failed: ${e.toString()}');
    }
  }
  
  Future<void> logout() async {
    try {
      await _auth0.webAuthentication(scheme: AppConfig.appId).logout();
      await _clearTokens();
    } catch (e) {
      // Always clear local tokens even if logout fails
      await _clearTokens();
    }
  }
  
  Future<String?> getAccessToken() async {
    final token = await _storage.read(key: 'access_token');
    if (token == null) return null;
    
    // Check if token is expired and refresh if needed
    if (await _isTokenExpired(token)) {
      return await _refreshToken();
    }
    
    return token;
  }
  
  Future<String?> _refreshToken() async {
    try {
      final refreshToken = await _storage.read(key: 'refresh_token');
      if (refreshToken == null) return null;
      
      final credentials = await _auth0.api.renewCredentials(refreshToken);
      
      await _storage.write(key: 'access_token', value: credentials.accessToken);
      if (credentials.refreshToken != null) {
        await _storage.write(key: 'refresh_token', value: credentials.refreshToken);
      }
      
      return credentials.accessToken;
    } catch (e) {
      await _clearTokens();
      return null;
    }
  }
  
  Future<bool> _isTokenExpired(String token) async {
    // Decode JWT and check expiration
    final parts = token.split('.');
    if (parts.length != 3) return true;
    
    final payload = json.decode(
      utf8.decode(base64Url.decode(base64Url.normalize(parts[1])))
    );
    
    final exp = payload['exp'] as int?;
    if (exp == null) return true;
    
    final expirationDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    return DateTime.now().isAfter(expirationDate.subtract(Duration(minutes: 5)));
  }
  
  Future<void> _clearTokens() async {
    await _storage.deleteAll();
  }
}

class AuthException implements Exception {
  final String message;
  AuthException(this.message);
  
  @override
  String toString() => 'AuthException: $message';
}
```

### **Authentication State Management**
```dart
// lib/shared/providers/auth_provider.dart
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_provider.g.dart';

@Riverpod(keepAlive: true)
class AuthNotifier extends _$AuthNotifier {
  @override
  AuthState build() {
    _checkInitialAuthState();
    return const AuthState.initial();
  }
  
  Future<void> login() async {
    state = const AuthState.loading();
    
    try {
      final authService = ref.read(authServiceProvider);
      final user = await authService.login();
      
      if (user != null) {
        // Fetch user details from API
        final apiService = ref.read(apiServiceProvider);
        final userDetails = await apiService.getCurrentUser();
        
        state = AuthState.authenticated(userDetails);
      } else {
        state = const AuthState.unauthenticated();
      }
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }
  
  Future<void> logout() async {
    try {
      final authService = ref.read(authServiceProvider);
      await authService.logout();
    } finally {
      state = const AuthState.unauthenticated();
    }
  }
  
  Future<void> _checkInitialAuthState() async {
    try {
      final authService = ref.read(authServiceProvider);
      final token = await authService.getAccessToken();
      
      if (token != null) {
        final apiService = ref.read(apiServiceProvider);
        final user = await apiService.getCurrentUser();
        state = AuthState.authenticated(user);
      } else {
        state = const AuthState.unauthenticated();
      }
    } catch (e) {
      state = const AuthState.unauthenticated();
    }
  }
}

@freezed
class AuthState with _$AuthState {
  const factory AuthState.initial() = _Initial;
  const factory AuthState.loading() = _Loading;
  const factory AuthState.authenticated(User user) = _Authenticated;
  const factory AuthState.unauthenticated() = _Unauthenticated;
  const factory AuthState.error(String message) = _Error;
}
```

---

## 📡 **Offline Synchronization**

### **Offline Storage Strategy**
```dart
// lib/shared/services/offline_service.dart
import 'package:sqflite/sqflite.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class OfflineService {
  static Database? _database;
  static const String _dbName = 'fc_china_offline.db';
  static const int _dbVersion = 1;
  
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }
  
  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, _dbName);
    
    return await openDatabase(
      path,
      version: _dbVersion,
      onCreate: _createTables,
      onUpgrade: _upgradeDatabase,
    );
  }
  
  Future<void> _createTables(Database db, int version) async {
    // Products table
    await db.execute('''
      CREATE TABLE products (
        id TEXT PRIMARY KEY,
        factory_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        base_price REAL NOT NULL,
        currency TEXT NOT NULL,
        stock_quantity INTEGER NOT NULL,
        images TEXT, -- JSON array
        category_id TEXT,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL DEFAULT 'synced',
        last_modified INTEGER NOT NULL
      )
    ''');
    
    // Orders table
    await db.execute('''
      CREATE TABLE orders (
        id TEXT PRIMARY KEY,
        factory_id TEXT NOT NULL,
        customer_id TEXT NOT NULL,
        order_number TEXT NOT NULL,
        status TEXT NOT NULL,
        total_amount REAL NOT NULL,
        currency TEXT NOT NULL,
        items TEXT NOT NULL, -- JSON array
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        sync_status TEXT NOT NULL DEFAULT 'synced',
        last_modified INTEGER NOT NULL
      )
    ''');
    
    // Messages table
    await db.execute('''
      CREATE TABLE messages (
        id TEXT PRIMARY KEY,
        sender_id TEXT NOT NULL,
        receiver_id TEXT NOT NULL,
        content TEXT NOT NULL,
        type TEXT NOT NULL,
        attachments TEXT, -- JSON array
        created_at TEXT NOT NULL,
        sync_status TEXT NOT NULL DEFAULT 'synced',
        last_modified INTEGER NOT NULL
      )
    ''');
    
    // Sync queue table
    await db.execute('''
      CREATE TABLE sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        entity_type TEXT NOT NULL,
        entity_id TEXT NOT NULL,
        operation TEXT NOT NULL, -- 'create', 'update', 'delete'
        data TEXT NOT NULL, -- JSON data
        created_at INTEGER NOT NULL,
        retry_count INTEGER NOT NULL DEFAULT 0,
        last_error TEXT
      )
    ''');
  }
  
  // Cache data for offline access
  Future<void> cacheProducts(List<Product> products) async {
    final db = await database;
    final batch = db.batch();
    
    for (final product in products) {
      batch.insert(
        'products',
        {
          ...product.toJson(),
          'images': json.encode(product.images),
          'sync_status': 'synced',
          'last_modified': DateTime.now().millisecondsSinceEpoch,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    
    await batch.commit();
  }
  
  // Get cached products
  Future<List<Product>> getCachedProducts({String? factoryId}) async {
    final db = await database;
    final where = factoryId != null ? 'factory_id = ?' : null;
    final whereArgs = factoryId != null ? [factoryId] : null;
    
    final maps = await db.query(
      'products',
      where: where,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
    );
    
    return maps.map((map) {
      final productMap = Map<String, dynamic>.from(map);
      productMap['images'] = json.decode(map['images'] as String? ?? '[]');
      return Product.fromJson(productMap);
    }).toList();
  }
  
  // Queue operation for sync
  Future<void> queueForSync({
    required String entityType,
    required String entityId,
    required String operation,
    required Map<String, dynamic> data,
  }) async {
    final db = await database;
    await db.insert('sync_queue', {
      'entity_type': entityType,
      'entity_id': entityId,
      'operation': operation,
      'data': json.encode(data),
      'created_at': DateTime.now().millisecondsSinceEpoch,
    });
  }
  
  // Sync pending operations
  Future<void> syncPendingOperations() async {
    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity == ConnectivityResult.none) return;
    
    final db = await database;
    final pendingOps = await db.query(
      'sync_queue',
      orderBy: 'created_at ASC',
      limit: 50, // Process in batches
    );
    
    for (final op in pendingOps) {
      try {
        await _processSyncOperation(op);
        await db.delete('sync_queue', where: 'id = ?', whereArgs: [op['id']]);
      } catch (e) {
        // Update retry count and error
        await db.update(
          'sync_queue',
          {
            'retry_count': (op['retry_count'] as int) + 1,
            'last_error': e.toString(),
          },
          where: 'id = ?',
          whereArgs: [op['id']],
        );
        
        // Remove after 5 failed attempts
        if ((op['retry_count'] as int) >= 5) {
          await db.delete('sync_queue', where: 'id = ?', whereArgs: [op['id']]);
        }
      }
    }
  }
  
  Future<void> _processSyncOperation(Map<String, dynamic> op) async {
    final apiService = GetIt.instance<ApiService>();
    final entityType = op['entity_type'] as String;
    final operation = op['operation'] as String;
    final data = json.decode(op['data'] as String);
    
    switch (entityType) {
      case 'product':
        switch (operation) {
          case 'create':
            await apiService.createProduct(Product.fromJson(data));
            break;
          case 'update':
            await apiService.updateProduct(data['id'], data);
            break;
          case 'delete':
            await apiService.deleteProduct(data['id']);
            break;
        }
        break;
      case 'order':
        switch (operation) {
          case 'create':
            await apiService.createOrder(Order.fromJson(data));
            break;
          case 'update':
            await apiService.updateOrder(data['id'], data);
            break;
        }
        break;
      case 'message':
        switch (operation) {
          case 'create':
            await apiService.sendMessage(Message.fromJson(data));
            break;
        }
        break;
    }
  }
}
```

### **Offline-First Data Provider**
```dart
// lib/shared/providers/products_provider.dart
@riverpod
class ProductsNotifier extends _$ProductsNotifier {
  @override
  Future<List<Product>> build(String factoryId) async {
    // Always return cached data first
    final offlineService = ref.read(offlineServiceProvider);
    final cachedProducts = await offlineService.getCachedProducts(factoryId: factoryId);
    
    // Try to fetch fresh data in background
    _fetchFreshData(factoryId);
    
    return cachedProducts;
  }
  
  Future<void> _fetchFreshData(String factoryId) async {
    try {
      final connectivity = await Connectivity().checkConnectivity();
      if (connectivity == ConnectivityResult.none) return;
      
      final apiService = ref.read(apiServiceProvider);
      final freshProducts = await apiService.getProducts(factoryId);
      
      // Cache fresh data
      final offlineService = ref.read(offlineServiceProvider);
      await offlineService.cacheProducts(freshProducts);
      
      // Update state with fresh data
      state = AsyncValue.data(freshProducts);
    } catch (e) {
      // Keep cached data, don't update state on error
      print('Failed to fetch fresh data: $e');
    }
  }
  
  Future<void> createProduct(Product product) async {
    final connectivity = await Connectivity().checkConnectivity();
    final offlineService = ref.read(offlineServiceProvider);
    
    if (connectivity == ConnectivityResult.none) {
      // Queue for sync when online
      await offlineService.queueForSync(
        entityType: 'product',
        entityId: product.id,
        operation: 'create',
        data: product.toJson(),
      );
      
      // Add to local cache with pending status
      final currentProducts = state.value ?? [];
      final updatedProducts = [...currentProducts, product.copyWith(syncStatus: 'pending')];
      state = AsyncValue.data(updatedProducts);
    } else {
      try {
        final apiService = ref.read(apiServiceProvider);
        final createdProduct = await apiService.createProduct(product);
        
        // Update cache and state
        await offlineService.cacheProducts([createdProduct]);
        final currentProducts = state.value ?? [];
        final updatedProducts = [...currentProducts, createdProduct];
        state = AsyncValue.data(updatedProducts);
      } catch (e) {
        // Queue for retry
        await offlineService.queueForSync(
          entityType: 'product',
          entityId: product.id,
          operation: 'create',
          data: product.toJson(),
        );
        throw e;
      }
    }
  }
}
```

---

## 🚀 **Mobile App Store Deployment (Android & iOS)**

### **iOS Mobile Deployment Configuration**
```ruby
# ios/Fastfile
default_platform(:ios)

platform :ios do
  desc "Build and upload Factory mobile app to TestFlight"
  lane :factory_mobile_beta do
    setup_ci if ENV['CI']

    match(
      type: "appstore",
      app_identifier: "com.fcchina.factory.mobile",
      readonly: true
    )

    build_app(
      scheme: "Factory",
      export_method: "app-store",
      export_options: {
        provisioningProfiles: {
          "com.fcchina.factory.mobile" => "match AppStore com.fcchina.factory.mobile"
        }
      }
    )

    upload_to_testflight(
      app_identifier: "com.fcchina.factory.mobile",
      skip_waiting_for_build_processing: true
    )
  end

  desc "Build and upload Customer mobile app to TestFlight"
  lane :customer_mobile_beta do
    setup_ci if ENV['CI']

    match(
      type: "appstore",
      app_identifier: "com.fcchina.customer.mobile",
      readonly: true
    )

    build_app(
      scheme: "Customer",
      export_method: "app-store",
      export_options: {
        provisioningProfiles: {
          "com.fcchina.customer.mobile" => "match AppStore com.fcchina.customer.mobile"
        }
      }
    )

    upload_to_testflight(
      app_identifier: "com.fcchina.customer.mobile",
      skip_waiting_for_build_processing: true
    )
  end
end
```

### **Android Mobile Deployment Configuration**
```gradle
// android/app/build.gradle
android {
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    flavorDimensions "app"
    productFlavors {
        factory {
            dimension "app"
            applicationId "com.fcchina.factory.mobile"
            versionNameSuffix "-factory-mobile"
            resValue "string", "app_name", "FC-CHINA Factory Mobile"
        }
        customer {
            dimension "app"
            applicationId "com.fcchina.customer.mobile"
            versionNameSuffix "-customer-mobile"
            resValue "string", "app_name", "FC-CHINA Mobile"
        }
    }
}
```

### **CI/CD Pipeline for Mobile Apps (Android & iOS)**
```yaml
# .github/workflows/mobile-deploy.yml
name: Mobile App Deployment (Android & iOS)

on:
  push:
    branches: [main]
    paths: ['apps/mobile/**']

  workflow_dispatch:
    inputs:
      deploy_target:
        description: 'Mobile deployment target'
        required: true
        default: 'testflight'
        type: choice
        options:
          - testflight
          - play-console
          - both

jobs:
  test-mobile:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'

      - name: Install mobile dependencies
        run: flutter pub get
        working-directory: apps/mobile

      - name: Run mobile tests
        run: flutter test
        working-directory: apps/mobile

  build-ios-mobile:
    needs: test-mobile
    runs-on: macos-latest
    if: github.event.inputs.deploy_target == 'testflight' || github.event.inputs.deploy_target == 'both' || github.event.inputs.deploy_target == ''

    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'

      - name: Install mobile dependencies
        run: flutter pub get
        working-directory: apps/mobile

      - name: Setup Ruby for iOS mobile deployment
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'
          bundler-cache: true
          working-directory: apps/mobile/ios

      - name: Deploy Factory mobile app to TestFlight
        run: bundle exec fastlane factory_mobile_beta
        working-directory: apps/mobile/ios
        env:
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}

      - name: Deploy Customer mobile app to TestFlight
        run: bundle exec fastlane customer_mobile_beta
        working-directory: apps/mobile/ios
        env:
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}

  build-android-mobile:
    needs: test-mobile
    runs-on: ubuntu-latest
    if: github.event.inputs.deploy_target == 'play-console' || github.event.inputs.deploy_target == 'both' || github.event.inputs.deploy_target == ''

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'

      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'

      - name: Install mobile dependencies
        run: flutter pub get
        working-directory: apps/mobile

      - name: Setup Android mobile signing
        run: |
          echo "${{ secrets.ANDROID_KEYSTORE }}" | base64 --decode > android/app/keystore.jks
          echo "storeFile=keystore.jks" >> android/key.properties
          echo "keyAlias=${{ secrets.ANDROID_KEY_ALIAS }}" >> android/key.properties
          echo "storePassword=${{ secrets.ANDROID_STORE_PASSWORD }}" >> android/key.properties
          echo "keyPassword=${{ secrets.ANDROID_KEY_PASSWORD }}" >> android/key.properties
        working-directory: apps/mobile

      - name: Build Factory mobile APK
        run: flutter build apk --release --flavor factory
        working-directory: apps/mobile

      - name: Build Customer mobile APK
        run: flutter build apk --release --flavor customer
        working-directory: apps/mobile

      - name: Upload Factory mobile app to Play Console
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.fcchina.factory.mobile
          releaseFiles: apps/mobile/build/app/outputs/flutter-apk/app-factory-release.apk
          track: internal

      - name: Upload Customer mobile app to Play Console
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.fcchina.customer.mobile
          releaseFiles: apps/mobile/build/app/outputs/flutter-apk/app-customer-release.apk
          track: internal
```
