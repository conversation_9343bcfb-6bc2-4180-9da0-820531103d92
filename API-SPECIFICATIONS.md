# FC-CHINA API Specifications

## 📋 **API Overview**

### **Base URLs**
- **Development**: `http://localhost:3001`
- **Staging**: `https://api-staging.fc-china.com`
- **Production**: `https://api.fc-china.com`

### **API Versioning**
- **Current Version**: v2
- **Supported Versions**: v1, v2
- **Version Header**: `API-Version: v2`
- **URL Versioning**: `/api/v2/trpc/...`

### **Authentication**
- **Type**: Bear<PERSON> (JWT from Auth0)
- **Header**: `Authorization: Bearer <token>`
- **Scope**: Required for all protected endpoints

---

## 📁 **File Upload API**

### **Generate Upload URL**
```typescript
// Endpoint: POST /api/trpc/uploads.generateUploadUrl
interface GenerateUploadUrlInput {
  fileName: string;
  fileType: string;
  fileSize: number; // Max 10MB (10485760 bytes)
  uploadType: 'product-image' | 'document' | 'avatar' | 'factory-logo';
  metadata?: {
    alt?: string;
    description?: string;
    tags?: string[];
  };
}

interface GenerateUploadUrlOutput {
  uploadUrl: string;      // Signed URL for direct upload
  filePath: string;       // Internal file path
  publicUrl: string;      // Public access URL
  expiresAt: string;      // Upload URL expiration (ISO 8601)
  maxFileSize: number;    // Maximum allowed file size
}

// Example Request
{
  "input": {
    "fileName": "product-image.jpg",
    "fileType": "image/jpeg",
    "fileSize": 2048576,
    "uploadType": "product-image",
    "metadata": {
      "alt": "Red widget product photo",
      "description": "Main product image showing red widget",
      "tags": ["product", "red", "widget"]
    }
  }
}

// Example Response
{
  "result": {
    "data": {
      "uploadUrl": "https://supabase.co/storage/v1/upload/sign/...",
      "filePath": "factory-123/product-image/1703123456789-abc123.jpg",
      "publicUrl": "https://supabase.co/storage/v1/object/public/fc-china-uploads/factory-123/product-image/1703123456789-abc123.jpg",
      "expiresAt": "2024-01-01T12:00:00.000Z",
      "maxFileSize": 10485760
    }
  }
}
```

### **Confirm Upload**
```typescript
// Endpoint: POST /api/trpc/uploads.confirmUpload
interface ConfirmUploadInput {
  filePath: string;
  uploadType: 'product-image' | 'document' | 'avatar' | 'factory-logo';
  metadata?: {
    originalName: string;
    size: number;
    mimeType: string;
    dimensions?: { width: number; height: number };
    checksum?: string;
  };
}

interface ConfirmUploadOutput {
  id: string;
  path: string;
  publicUrl: string;
  type: string;
  size: number;
  metadata: Record<string, any>;
  createdAt: string;
  processedVariants?: {
    thumbnail: string;
    medium: string;
    large: string;
  };
}
```

### **File Upload Error Codes**
```typescript
enum FileUploadErrorCode {
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  UPLOAD_EXPIRED = 'UPLOAD_EXPIRED',
  VIRUS_DETECTED = 'VIRUS_DETECTED',
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED',
  PROCESSING_FAILED = 'PROCESSING_FAILED'
}

// Error Response Example
{
  "error": {
    "code": "BAD_REQUEST",
    "message": "File type image/gif not allowed for product-image",
    "data": {
      "code": "INVALID_FILE_TYPE",
      "allowedTypes": ["image/jpeg", "image/png", "image/webp"],
      "receivedType": "image/gif"
    }
  }
}
```

---

## 📦 **Bulk Operations API**

### **Bulk Product Import**
```typescript
// Endpoint: POST /api/trpc/products.bulkImport
interface BulkImportInput {
  format: 'csv' | 'json' | 'xlsx';
  data: string | object[]; // CSV string or JSON array
  options: {
    skipErrors: boolean;      // Continue on individual errors
    validateOnly: boolean;    // Validate without importing
    updateExisting: boolean;  // Update if product exists
    batchSize: number;        // Process in batches (default: 100)
  };
  mapping?: {
    [csvColumn: string]: string; // Map CSV columns to product fields
  };
}

interface BulkImportOutput {
  jobId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  totalRecords: number;
  processedRecords: number;
  successCount: number;
  errorCount: number;
  errors: Array<{
    row: number;
    field?: string;
    message: string;
    data: any;
  }>;
  estimatedCompletion?: string; // ISO 8601
}

// Example CSV Import
{
  "input": {
    "format": "csv",
    "data": "name,description,price,category\nWidget A,Red widget,99.99,Electronics\nWidget B,Blue widget,149.99,Electronics",
    "options": {
      "skipErrors": true,
      "validateOnly": false,
      "updateExisting": false,
      "batchSize": 50
    },
    "mapping": {
      "name": "name",
      "description": "description", 
      "price": "basePrice",
      "category": "categoryName"
    }
  }
}
```

### **Bulk Product Update**
```typescript
// Endpoint: POST /api/trpc/products.bulkUpdate
interface BulkUpdateInput {
  filters: {
    categoryId?: string;
    status?: ProductStatus;
    priceRange?: { min: number; max: number };
    tags?: string[];
  };
  updates: {
    status?: ProductStatus;
    basePrice?: number;
    stockQuantity?: number;
    tags?: string[];
    specifications?: Record<string, any>;
  };
  options: {
    dryRun: boolean;         // Preview changes without applying
    batchSize: number;       // Update in batches
    notifyUsers: boolean;    // Send notifications for changes
  };
}

interface BulkUpdateOutput {
  jobId: string;
  affectedCount: number;
  previewChanges?: Array<{
    productId: string;
    productName: string;
    changes: Record<string, { from: any; to: any }>;
  }>;
  status: 'queued' | 'processing' | 'completed' | 'failed';
}
```

### **Bulk Export**
```typescript
// Endpoint: POST /api/trpc/products.bulkExport
interface BulkExportInput {
  format: 'csv' | 'json' | 'xlsx';
  filters: {
    categoryId?: string;
    status?: ProductStatus[];
    dateRange?: { from: string; to: string };
    includeInactive?: boolean;
  };
  fields: string[]; // Specific fields to export
  options: {
    includeImages: boolean;
    includeVariants: boolean;
    compression: 'none' | 'zip' | 'gzip';
  };
}

interface BulkExportOutput {
  jobId: string;
  downloadUrl?: string;     // Available when completed
  expiresAt?: string;       // Download URL expiration
  fileSize?: number;        // File size in bytes
  recordCount: number;
  status: 'queued' | 'processing' | 'completed' | 'failed';
}
```

### **Job Status Tracking**
```typescript
// Endpoint: GET /api/trpc/jobs.getStatus
interface JobStatusInput {
  jobId: string;
}

interface JobStatusOutput {
  id: string;
  type: 'import' | 'export' | 'update' | 'delete';
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: {
    current: number;
    total: number;
    percentage: number;
  };
  result?: any;             // Job-specific result data
  error?: string;           // Error message if failed
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  estimatedCompletion?: string;
}
```

---

## ⚠️ **Error Handling Specifications**

### **Standard Error Response Format**
```typescript
interface TRPCErrorResponse {
  error: {
    code: TRPCErrorCode;
    message: string;
    data?: {
      code?: string;          // Application-specific error code
      field?: string;         // Field that caused the error
      details?: any;          // Additional error details
      stack?: string;         // Stack trace (development only)
      errorId?: string;       // Unique error identifier for tracking
    };
  };
}

// TRPC Error Codes
type TRPCErrorCode = 
  | 'BAD_REQUEST'           // 400 - Invalid input
  | 'UNAUTHORIZED'          // 401 - Authentication required
  | 'FORBIDDEN'             // 403 - Insufficient permissions
  | 'NOT_FOUND'             // 404 - Resource not found
  | 'METHOD_NOT_SUPPORTED'  // 405 - HTTP method not allowed
  | 'TIMEOUT'               // 408 - Request timeout
  | 'CONFLICT'              // 409 - Resource conflict
  | 'PRECONDITION_FAILED'   // 412 - Precondition not met
  | 'PAYLOAD_TOO_LARGE'     // 413 - Request payload too large
  | 'UNPROCESSABLE_CONTENT' // 422 - Validation error
  | 'TOO_MANY_REQUESTS'     // 429 - Rate limit exceeded
  | 'CLIENT_CLOSED_REQUEST' // 499 - Client closed request
  | 'INTERNAL_SERVER_ERROR' // 500 - Server error
  | 'NOT_IMPLEMENTED'       // 501 - Feature not implemented
  | 'BAD_GATEWAY'           // 502 - Upstream error
  | 'SERVICE_UNAVAILABLE'   // 503 - Service temporarily unavailable
  | 'GATEWAY_TIMEOUT';      // 504 - Upstream timeout
```

### **Application-Specific Error Codes**
```typescript
enum ApplicationErrorCode {
  // Authentication & Authorization
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  FACTORY_ACCESS_DENIED = 'FACTORY_ACCESS_DENIED',
  
  // Validation Errors
  REQUIRED_FIELD_MISSING = 'REQUIRED_FIELD_MISSING',
  INVALID_FORMAT = 'INVALID_FORMAT',
  VALUE_OUT_OF_RANGE = 'VALUE_OUT_OF_RANGE',
  DUPLICATE_VALUE = 'DUPLICATE_VALUE',
  
  // Business Logic Errors
  INSUFFICIENT_STOCK = 'INSUFFICIENT_STOCK',
  ORDER_ALREADY_CONFIRMED = 'ORDER_ALREADY_CONFIRMED',
  PRODUCT_NOT_AVAILABLE = 'PRODUCT_NOT_AVAILABLE',
  MINIMUM_ORDER_NOT_MET = 'MINIMUM_ORDER_NOT_MET',
  
  // File Upload Errors
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  VIRUS_DETECTED = 'VIRUS_DETECTED',
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED',
  
  // External Service Errors
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  EMAIL_DELIVERY_FAILED = 'EMAIL_DELIVERY_FAILED',
  SMS_DELIVERY_FAILED = 'SMS_DELIVERY_FAILED',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  
  // System Errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR'
}
```

### **Error Response Examples**
```typescript
// Validation Error
{
  "error": {
    "code": "UNPROCESSABLE_CONTENT",
    "message": "Validation failed",
    "data": {
      "code": "REQUIRED_FIELD_MISSING",
      "field": "basePrice",
      "details": {
        "received": null,
        "expected": "positive number"
      },
      "errorId": "err_1703123456789_abc123"
    }
  }
}

// Business Logic Error
{
  "error": {
    "code": "BAD_REQUEST", 
    "message": "Insufficient stock for order",
    "data": {
      "code": "INSUFFICIENT_STOCK",
      "details": {
        "productId": "prod_123",
        "requestedQuantity": 100,
        "availableStock": 50,
        "minimumOrderQuantity": 10
      },
      "errorId": "err_1703123456790_def456"
    }
  }
}

// Rate Limiting Error
{
  "error": {
    "code": "TOO_MANY_REQUESTS",
    "message": "Rate limit exceeded",
    "data": {
      "code": "RATE_LIMIT_EXCEEDED",
      "details": {
        "limit": 100,
        "window": "1 minute",
        "retryAfter": 45,
        "resetTime": "2024-01-01T12:01:00.000Z"
      },
      "errorId": "err_1703123456791_ghi789"
    }
  }
}
```

---

## 🔄 **Pagination & Filtering**

### **Standard Pagination**
```typescript
interface PaginationInput {
  page: number;        // 1-based page number (default: 1)
  limit: number;       // Items per page (default: 20, max: 100)
  sortBy?: string;     // Field to sort by
  sortOrder?: 'asc' | 'desc'; // Sort direction (default: 'desc')
}

interface PaginationOutput<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;      // Total number of items
    pages: number;      // Total number of pages
    hasNext: boolean;   // Has next page
    hasPrev: boolean;   // Has previous page
  };
}
```

### **Cursor-based Pagination**
```typescript
interface CursorPaginationInput {
  cursor?: string;     // Cursor for next page
  limit: number;       // Items per page (default: 20, max: 100)
  sortBy?: string;     // Field to sort by
  sortOrder?: 'asc' | 'desc';
}

interface CursorPaginationOutput<T> {
  data: T[];
  pagination: {
    nextCursor?: string;  // Cursor for next page
    prevCursor?: string;  // Cursor for previous page
    hasNext: boolean;
    hasPrev: boolean;
    limit: number;
  };
}
```

### **Advanced Filtering**
```typescript
interface FilterInput {
  search?: string;           // Full-text search
  filters?: {
    [field: string]: {
      eq?: any;              // Equals
      ne?: any;              // Not equals
      gt?: any;              // Greater than
      gte?: any;             // Greater than or equal
      lt?: any;              // Less than
      lte?: any;             // Less than or equal
      in?: any[];            // In array
      nin?: any[];           // Not in array
      contains?: string;     // String contains
      startsWith?: string;   // String starts with
      endsWith?: string;     // String ends with
      between?: [any, any];  // Between two values
    };
  };
  dateRange?: {
    field: string;           // Date field to filter
    from?: string;           // ISO 8601 date
    to?: string;             // ISO 8601 date
  };
}

// Example: Filter products
{
  "search": "widget",
  "filters": {
    "basePrice": {
      "between": [50, 200]
    },
    "status": {
      "in": ["ACTIVE", "PENDING_APPROVAL"]
    },
    "category.name": {
      "eq": "Electronics"
    }
  },
  "dateRange": {
    "field": "createdAt",
    "from": "2024-01-01T00:00:00.000Z",
    "to": "2024-12-31T23:59:59.999Z"
  }
}
```
