# FC-CHINA Development Environment Setup

## 📋 **Prerequisites**

### **Required Software**
- **Node.js**: v20.x or higher ([Download](https://nodejs.org/))
- **npm**: v10.x or higher (comes with Node.js)
- **Docker**: Latest version ([Download](https://www.docker.com/))
- **Docker Compose**: v2.x or higher
- **Git**: Latest version ([Download](https://git-scm.com/))

### **Recommended Tools**
- **VS Code**: With recommended extensions (see `.vscode/extensions.json`)
- **Postman**: For API testing
- **TablePlus** or **pgAdmin**: For database management
- **Redis Insight**: For Redis management

### **System Requirements**
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space
- **OS**: macOS, Windows 10/11, or Linux (for development only - no desktop app deployment)

---

## 🚀 **Quick Start**

### **1. Clone Repository**
```bash
git clone https://github.com/your-org/fc-china.git
cd fc-china
```

### **2. Install Dependencies**
```bash
# Install root dependencies
npm install

# Install workspace dependencies
npm run install:all
```

### **3. Environment Configuration**
```bash
# Copy environment template
cp .env.example .env.local

# Edit environment variables
nano .env.local
```

### **4. Start Development Environment**
```bash
# Start all services with Docker
docker-compose up -d

# Or start individual services
npm run dev:db      # Database only
npm run dev:api     # API server
npm run dev:web     # Web frontend
npm run dev:all     # All services
```

### **5. Database Setup**
```bash
# Generate Prisma client
npm run db:generate

# Run database migrations
npm run db:migrate

# Seed development data
npm run db:seed
```

### **6. Verify Installation**
- **Web App**: http://localhost:3000
- **API Server**: http://localhost:3001
- **Prisma Studio**: http://localhost:5555
- **Mailhog**: http://localhost:8025
- **MinIO Console**: http://localhost:9001

---

## 🔧 **Detailed Setup Instructions**

### **Environment Variables Configuration**

#### **Required Variables**
```bash
# Database
DATABASE_URL="postgresql://fc_china_user:fc_china_password@localhost:5432/fc_china_dev"

# Supabase (for production-like setup)
SUPABASE_URL="your-supabase-url"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_KEY="your-service-key"

# Auth0
AUTH0_DOMAIN="your-domain.auth0.com"
AUTH0_CLIENT_ID="your-client-id"
AUTH0_CLIENT_SECRET="your-client-secret"
AUTH0_AUDIENCE="https://api.fc-china.com"
```

#### **Optional Variables**
```bash
# Redis (if not using Docker)
REDIS_URL="redis://localhost:6379"

# Email (for testing)
SMTP_HOST="localhost"
SMTP_PORT="1025"
```

### **Database Setup**

#### **Using Docker (Recommended)**
```bash
# Start PostgreSQL container
docker-compose up -d postgres

# Wait for database to be ready
npm run wait-for-db

# Run migrations
npm run db:migrate

# Seed data
npm run db:seed
```

#### **Using Local PostgreSQL**
```bash
# Install PostgreSQL
brew install postgresql  # macOS
sudo apt install postgresql  # Ubuntu

# Create database
createdb fc_china_dev

# Update DATABASE_URL in .env.local
DATABASE_URL="postgresql://username:password@localhost:5432/fc_china_dev"
```

### **Auth0 Configuration**

#### **Create Auth0 Application**
1. Go to [Auth0 Dashboard](https://manage.auth0.com/)
2. Create new application (Single Page Application)
3. Configure allowed URLs:
   - **Allowed Callback URLs**: `http://localhost:3000/api/auth/callback`
   - **Allowed Logout URLs**: `http://localhost:3000`
   - **Allowed Web Origins**: `http://localhost:3000`

#### **Create Auth0 API**
1. Go to APIs section in Auth0 Dashboard
2. Create new API with identifier: `https://api.fc-china.com`
3. Enable RBAC and add permissions

### **Supabase Setup (Optional)**

#### **For Production-like Development**
1. Create project at [Supabase](https://supabase.com/)
2. Get project URL and API keys
3. Configure storage bucket for file uploads
4. Set up Row Level Security policies

---

## 📱 **Mobile Development Setup (Android & iOS Only)**

### **Flutter Mobile Environment**
```bash
# Install Flutter for mobile development
git clone https://github.com/flutter/flutter.git -b stable
export PATH="$PATH:`pwd`/flutter/bin"

# Verify mobile platform setup
flutter doctor

# Install mobile app dependencies
cd apps/mobile
flutter pub get
```

### **iOS Mobile Setup (macOS only)**
```bash
# Install Xcode from App Store (for iOS mobile development)
# Install CocoaPods for iOS mobile dependencies
sudo gem install cocoapods

# Setup iOS mobile dependencies
cd apps/mobile/ios
pod install
```

### **Android Mobile Setup**
```bash
# Install Android Studio (for Android mobile development)
# Install Android SDK and mobile emulators
# Accept Android mobile licenses
flutter doctor --android-licenses
```

---

## 🧪 **Testing Setup**

### **Unit Tests**
```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### **E2E Tests**
```bash
# Install Playwright
npx playwright install

# Run E2E tests
npm run test:e2e

# Run E2E tests in headed mode
npm run test:e2e:headed
```

### **Mobile Tests (Android & iOS)**
```bash
# Flutter mobile tests
cd apps/mobile
flutter test

# Mobile integration tests
flutter drive --target=test_driver/app.dart
```

---

## 🔍 **Development Tools**

### **VS Code Extensions**
Install recommended extensions:
```bash
code --install-extension bradlc.vscode-tailwindcss
code --install-extension prisma.prisma
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension esbenp.prettier-vscode
code --install-extension ms-vscode.vscode-eslint
```

### **Database Management**
```bash
# Prisma Studio (recommended)
npm run db:studio

# Or use TablePlus/pgAdmin
# Connection: localhost:5432
# Database: fc_china_dev
# User: fc_china_user
# Password: fc_china_password
```

### **API Testing**
```bash
# Import Postman collection
# File: ./docs/postman/FC-CHINA-API.postman_collection.json

# Or use curl examples
curl -X GET http://localhost:3001/api/health
```

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **Port Already in Use**
```bash
# Kill process on port 3000
lsof -ti:3000 | xargs kill -9

# Or use different ports
PORT=3002 npm run dev:web
```

#### **Database Connection Issues**
```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Reset database
npm run db:reset
```

#### **Node Modules Issues**
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install
```

#### **Prisma Issues**
```bash
# Regenerate Prisma client
npm run db:generate

# Reset Prisma
npm run db:reset
```

### **Getting Help**
- Check [GitHub Issues](https://github.com/your-org/fc-china/issues)
- Join development Slack channel
- Review [Technical Documentation](./FC-CHINA-TECHNICAL-DESIGN.md)

---

## 📚 **Next Steps**

1. **Read Documentation**: Review all documentation in `/docs`
2. **Explore Codebase**: Start with `/apps/web/src/pages`
3. **Run Tests**: Ensure all tests pass
4. **Make Changes**: Follow development workflow
5. **Submit PR**: Use PR template and follow review process

---

## 🔄 **Development Workflow**

### **Daily Workflow**
```bash
# 1. Pull latest changes
git pull origin main

# 2. Install any new dependencies
npm install

# 3. Start development environment
npm run dev:all

# 4. Run tests before making changes
npm run test

# 5. Make your changes
# 6. Run tests again
npm run test

# 7. Commit and push
git add .
git commit -m "feat: your feature description"
git push origin feature-branch
```

### **Before Submitting PR**
```bash
# Run full test suite
npm run test:all

# Check code quality
npm run lint
npm run type-check

# Build to ensure no build errors
npm run build
```
