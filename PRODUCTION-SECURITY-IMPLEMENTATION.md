# Production Security Implementation

## 🔐 **Enterprise Security Standards**

### **Authentication & Authorization**

#### **Production-Grade Auth Middleware**
```typescript
// src/lib/auth/middleware.ts
import jwt from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';
import { TRPCError } from '@trpc/server';
import { rateLimit } from 'express-rate-limit';
import { config } from '../config';
import { AuditLogger } from '../logging/audit';

const client = jwksClient({
  jwksUri: `https://${config.AUTH0_DOMAIN}/.well-known/jwks.json`,
  cache: true,
  cacheMaxEntries: 5,
  cacheMaxAge: 600000, // 10 minutes
  rateLimit: true,
  jwksRequestsPerMinute: 5,
  jwksUri: `https://${config.AUTH0_DOMAIN}/.well-known/jwks.json`,
});

interface DecodedToken {
  sub: string;
  aud: string;
  iss: string;
  exp: number;
  iat: number;
  scope: string;
  permissions?: string[];
  'https://fc-china.com/factory_id'?: string;
  'https://fc-china.com/role'?: string;
}

export const requireAuth = t.middleware(async ({ ctx, next }) => {
  const authHeader = ctx.req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    await AuditLogger.logSecurityEvent({
      event: 'AUTH_MISSING_TOKEN',
      ip: ctx.req.ip,
      userAgent: ctx.req.headers['user-agent'],
      timestamp: new Date(),
    });
    
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    });
  }
  
  const token = authHeader.substring(7);
  
  try {
    // Verify JWT signature and claims
    const decoded = await verifyToken(token);
    
    // Additional security validations
    if (!decoded.sub || !decoded.aud || decoded.aud !== config.AUTH0_AUDIENCE) {
      throw new Error('Invalid token claims');
    }
    
    // Check token expiration with buffer
    const now = Math.floor(Date.now() / 1000);
    if (decoded.exp <= now + 30) { // 30 second buffer
      throw new Error('Token expired or expiring soon');
    }
    
    // Fetch user from database with security context
    const user = await ctx.db.user.findUnique({
      where: { auth0Id: decoded.sub },
      include: {
        factory: {
          select: {
            id: true,
            status: true,
            subscriptionStatus: true,
            securitySettings: true,
          }
        },
        permissions: true,
        sessions: {
          where: {
            isActive: true,
            expiresAt: { gt: new Date() }
          }
        }
      }
    });
    
    if (!user) {
      await AuditLogger.logSecurityEvent({
        event: 'AUTH_USER_NOT_FOUND',
        auth0Id: decoded.sub,
        ip: ctx.req.ip,
        timestamp: new Date(),
      });
      
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'User not found',
      });
    }
    
    // Check user status
    if (user.status !== 'ACTIVE') {
      await AuditLogger.logSecurityEvent({
        event: 'AUTH_INACTIVE_USER',
        userId: user.id,
        status: user.status,
        ip: ctx.req.ip,
        timestamp: new Date(),
      });
      
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'Account is not active',
      });
    }
    
    // Check factory status if applicable
    if (user.factory && user.factory.status !== 'ACTIVE') {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'Factory account is not active',
      });
    }
    
    // Log successful authentication
    await AuditLogger.logSecurityEvent({
      event: 'AUTH_SUCCESS',
      userId: user.id,
      factoryId: user.factory?.id,
      ip: ctx.req.ip,
      timestamp: new Date(),
    });
    
    return next({
      ctx: {
        ...ctx,
        user: {
          ...user,
          permissions: user.permissions.map(p => p.name),
          factoryId: user.factory?.id,
        }
      }
    });
    
  } catch (error) {
    await AuditLogger.logSecurityEvent({
      event: 'AUTH_FAILURE',
      error: error.message,
      ip: ctx.req.ip,
      timestamp: new Date(),
    });
    
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Invalid or expired token',
    });
  }
});

async function verifyToken(token: string): Promise<DecodedToken> {
  return new Promise((resolve, reject) => {
    jwt.verify(token, getKey, {
      audience: config.AUTH0_AUDIENCE,
      issuer: `https://${config.AUTH0_DOMAIN}/`,
      algorithms: ['RS256']
    }, (err, decoded) => {
      if (err) {
        reject(err);
      } else {
        resolve(decoded as DecodedToken);
      }
    });
  });
}

function getKey(header: any, callback: any) {
  client.getSigningKey(header.kid, (err, key) => {
    if (err) {
      callback(err);
    } else {
      const signingKey = key?.getPublicKey();
      callback(null, signingKey);
    }
  });
}
```

#### **Permission-Based Access Control**
```typescript
// src/lib/auth/permissions.ts
export enum Permission {
  // Factory Management
  FACTORY_READ = 'factory:read',
  FACTORY_WRITE = 'factory:write',
  FACTORY_DELETE = 'factory:delete',
  
  // Product Management
  PRODUCT_READ = 'product:read',
  PRODUCT_WRITE = 'product:write',
  PRODUCT_DELETE = 'product:delete',
  PRODUCT_PUBLISH = 'product:publish',
  
  // Order Management
  ORDER_READ = 'order:read',
  ORDER_WRITE = 'order:write',
  ORDER_PROCESS = 'order:process',
  
  // User Management
  USER_READ = 'user:read',
  USER_WRITE = 'user:write',
  USER_DELETE = 'user:delete',
  
  // Analytics
  ANALYTICS_READ = 'analytics:read',
  ANALYTICS_EXPORT = 'analytics:export',
  
  // System Administration
  SYSTEM_ADMIN = 'system:admin',
}

export const requirePermission = (permission: Permission) => {
  return t.middleware(async ({ ctx, next }) => {
    if (!ctx.user) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }
    
    if (!ctx.user.permissions.includes(permission)) {
      await AuditLogger.logSecurityEvent({
        event: 'PERMISSION_DENIED',
        userId: ctx.user.id,
        permission,
        ip: ctx.req.ip,
        timestamp: new Date(),
      });
      
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: `Permission required: ${permission}`,
      });
    }
    
    return next();
  });
};

export const requireFactoryAccess = t.middleware(async ({ ctx, next }) => {
  if (!ctx.user?.factoryId) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Factory access required',
    });
  }
  
  return next();
});
```

### **Rate Limiting & DDoS Protection**

#### **Advanced Rate Limiting**
```typescript
// src/lib/security/rate-limiting.ts
import { rateLimit } from 'express-rate-limit';
import { RedisStore } from 'rate-limit-redis';
import { redis } from '../redis';
import { config } from '../config';

// Global rate limiting
export const globalRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: config.RATE_LIMIT_WINDOW_MS,
  max: config.RATE_LIMIT_MAX_REQUESTS,
  message: {
    error: 'Too many requests',
    retryAfter: Math.ceil(config.RATE_LIMIT_WINDOW_MS / 1000),
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use user ID if authenticated, otherwise IP
    return req.user?.id || req.ip;
  },
});

// API-specific rate limiting
export const apiRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60000, // 1 minute
  max: (req) => {
    // Different limits based on user type
    if (req.user?.role === 'SYSTEM_ADMIN') return 1000;
    if (req.user?.factory?.subscriptionTier === 'PREMIUM') return 500;
    if (req.user?.factory?.subscriptionTier === 'STANDARD') return 200;
    return 100; // Default/free tier
  },
  keyGenerator: (req) => `api:${req.user?.id || req.ip}`,
});

// File upload rate limiting
export const uploadRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 60000, // 1 minute
  max: 10, // 10 uploads per minute
  keyGenerator: (req) => `upload:${req.user?.id || req.ip}`,
});

// Bulk operation rate limiting
export const bulkOperationRateLimit = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args),
  }),
  windowMs: 300000, // 5 minutes
  max: 5, // 5 bulk operations per 5 minutes
  keyGenerator: (req) => `bulk:${req.user?.id || req.ip}`,
});
```

### **Input Validation & Sanitization**

#### **Production-Grade Validation**
```typescript
// src/lib/validation/schemas.ts
import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

// Custom validation functions
const sanitizeHtml = (html: string) => DOMPurify.sanitize(html);
const sanitizeString = (str: string) => str.trim().replace(/[<>]/g, '');

// Enhanced validation schemas
export const createProductSchema = z.object({
  name: z.string()
    .min(1, 'Product name is required')
    .max(200, 'Product name too long')
    .transform(sanitizeString)
    .refine(name => !/[<>]/.test(name), 'Invalid characters in name'),
    
  description: z.string()
    .max(5000, 'Description too long')
    .transform(sanitizeHtml)
    .optional(),
    
  basePrice: z.number()
    .positive('Price must be positive')
    .max(1000000, 'Price too high')
    .multipleOf(0.01, 'Price must have at most 2 decimal places'),
    
  currency: z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY']),
  
  categoryId: z.string()
    .cuid('Invalid category ID')
    .refine(async (id) => {
      // Validate category exists and is active
      const category = await prisma.category.findFirst({
        where: { id, isActive: true }
      });
      return !!category;
    }, 'Invalid category'),
    
  specifications: z.record(z.string(), z.any())
    .refine(specs => Object.keys(specs).length <= 50, 'Too many specifications')
    .transform(specs => {
      // Sanitize specification values
      const sanitized: Record<string, any> = {};
      for (const [key, value] of Object.entries(specs)) {
        if (typeof value === 'string') {
          sanitized[sanitizeString(key)] = sanitizeString(value);
        } else {
          sanitized[sanitizeString(key)] = value;
        }
      }
      return sanitized;
    }),
    
  tags: z.array(z.string().max(50))
    .max(20, 'Too many tags')
    .transform(tags => tags.map(sanitizeString)),
    
  minimumOrderQuantity: z.number()
    .int('MOQ must be an integer')
    .positive('MOQ must be positive')
    .max(1000000, 'MOQ too high'),
    
  stockQuantity: z.number()
    .int('Stock must be an integer')
    .min(0, 'Stock cannot be negative')
    .max(1000000, 'Stock too high'),
    
  leadTimeDays: z.number()
    .int('Lead time must be an integer')
    .min(1, 'Lead time must be at least 1 day')
    .max(365, 'Lead time too long'),
});

// File upload validation
export const fileUploadSchema = z.object({
  fileName: z.string()
    .min(1, 'File name required')
    .max(255, 'File name too long')
    .refine(name => !/[<>:"/\\|?*]/.test(name), 'Invalid file name characters'),
    
  fileType: z.string()
    .refine(type => {
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/webp',
        'application/pdf', 'text/plain',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];
      return allowedTypes.includes(type);
    }, 'File type not allowed'),
    
  fileSize: z.number()
    .positive('File size must be positive')
    .max(10 * 1024 * 1024, 'File too large (max 10MB)'),
    
  uploadType: z.enum(['product-image', 'document', 'avatar', 'factory-logo']),
});
```

### **Security Headers & CSRF Protection**

#### **Security Middleware**
```typescript
// src/lib/security/headers.ts
import helmet from 'helmet';
import { config } from '../config';

export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", config.SUPABASE_URL],
      scriptSrc: ["'self'", "'unsafe-eval'"], // Remove unsafe-eval in production
      connectSrc: ["'self'", config.SUPABASE_URL, "https://api.fc-china.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: config.NODE_ENV === 'production' ? [] : null,
    },
    reportOnly: config.NODE_ENV !== 'production',
    reportUri: config.CSP_REPORT_URI,
  },
  
  hsts: {
    maxAge: config.HSTS_MAX_AGE,
    includeSubDomains: true,
    preload: true,
  },
  
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
});

// CSRF Protection
export const csrfProtection = (req: any, res: any, next: any) => {
  // Skip CSRF for API routes with proper authentication
  if (req.path.startsWith('/api/trpc') && req.headers.authorization) {
    return next();
  }
  
  // Implement CSRF token validation for form submissions
  const token = req.headers['x-csrf-token'] || req.body._csrf;
  const sessionToken = req.session?.csrfToken;
  
  if (!token || !sessionToken || token !== sessionToken) {
    return res.status(403).json({ error: 'Invalid CSRF token' });
  }
  
  next();
};
```
