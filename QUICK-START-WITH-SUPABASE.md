# 🚀 FC-CHINA Quick Start with Supabase

**Platform Scope**: This quick start covers:
- ✅ **Web Browser Application**: Responsive design for all browsers
- ✅ **Mobile Applications**: Flutter for Android and iOS smartphones/tablets
- ❌ **Desktop Applications**: No native desktop development

## ✅ **Your Supabase Project is Ready**

**Project**: fc-china  
**URL**: https://ejrxrhojmrjpjodogtxq.supabase.co  
**Status**: ✅ Connected to Augment

---

## 🎯 **Start Development in 15 Minutes**

### **Step 1: Get Missing Supabase Credentials (5 minutes)**

1. **Go to Supabase Dashboard**: https://supabase.com/dashboard/project/ejrxrhojmrjpjodogtxq

2. **Get Service Role Key**:
   - Navigate to **Settings** → **API**
   - Copy the **service_role** key (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)
   - Keep this secure - it has admin access to your database

3. **Get Database Password**:
   - Navigate to **Settings** → **Database**
   - Copy your database password (you set this when creating the project)
   - If you forgot it, you can reset it in the same section

4. **Create Storage Bucket**:
   - Navigate to **Storage**
   - Click **Create Bucket**
   - Name: `fc-china-uploads`
   - Set to **Public** bucket
   - Click **Create**

### **Step 2: Repository Setup (5 minutes)**

```bash
# 1. Clone your repository (replace with your actual repo URL)
git clone <your-repository-url>
cd fc-china

# 2. Install dependencies
npm install

# 3. Copy environment template
cp .env.example .env.local
```

### **Step 3: Configure Environment (3 minutes)**

Edit `.env.local` and update these values:

```bash
# =============================================================================
# DATABASE CONFIGURATION (Supabase)
# =============================================================================
DATABASE_URL="postgresql://postgres:[YOUR-DB-PASSWORD]@db.ejrxrhojmrjpjodogtxq.supabase.co:5432/postgres"
DIRECT_URL="postgresql://postgres:[YOUR-DB-PASSWORD]@db.ejrxrhojmrjpjodogtxq.supabase.co:5432/postgres"

# =============================================================================
# SUPABASE CONFIGURATION (Already configured)
# =============================================================================
SUPABASE_URL="https://ejrxrhojmrjpjodogtxq.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqcnhyaG9qbXJqcGpvZG9ndHhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDcwNTcsImV4cCI6MjA2ODU4MzA1N30.mqyGNK_0N0oawTGAyNQxAoH7nW8jrLRoSKmOTmySXAc"
SUPABASE_SERVICE_KEY="[PASTE-YOUR-SERVICE-ROLE-KEY-HERE]"

# =============================================================================
# REQUIRED FOR DEVELOPMENT
# =============================================================================
NEXTAUTH_SECRET="your-32-character-secret-key-here-generate-random"
JWT_SECRET="another-32-character-secret-key-here"
ENCRYPTION_KEY="32-character-encryption-key-here-hex-format"

# =============================================================================
# OPTIONAL (Can set up later)
# =============================================================================
AUTH0_DOMAIN="your-domain.auth0.com"
AUTH0_CLIENT_ID="your-client-id"
AUTH0_CLIENT_SECRET="your-client-secret"
AUTH0_AUDIENCE="https://api.fc-china.com"
```

**Generate Secrets Quickly**:
```bash
# Generate random secrets
node -e "console.log('NEXTAUTH_SECRET=' + require('crypto').randomBytes(32).toString('hex'))"
node -e "console.log('JWT_SECRET=' + require('crypto').randomBytes(32).toString('hex'))"
node -e "console.log('ENCRYPTION_KEY=' + require('crypto').randomBytes(32).toString('hex'))"
```

### **Step 4: Start Development (2 minutes)**

```bash
# 1. Initialize database schema
npm run db:generate
npm run db:push

# 2. Start all services
docker-compose up -d
npm run dev:all

# 3. Verify everything is working
curl http://localhost:3001/health
curl http://localhost:3000
```

---

## 🎉 **You're Ready to Develop!**

### **Access Your Development Environment**

- **Web App**: http://localhost:3000
- **API Server**: http://localhost:3001
- **Prisma Studio**: http://localhost:5555 (Database GUI)
- **Mailhog**: http://localhost:8025 (Email testing)
- **Supabase Dashboard**: https://supabase.com/dashboard/project/ejrxrhojmrjpjodogtxq

### **What's Working Now**

✅ **Database**: Connected to your Supabase PostgreSQL  
✅ **File Storage**: Supabase Storage bucket ready  
✅ **API Server**: tRPC API with production-grade security  
✅ **Web Frontend**: Next.js 15 with TypeScript  
✅ **Development Tools**: Prisma Studio, hot reload, etc.  

### **Next Development Steps**

1. **Follow Phase 1 Implementation Plan**: Start with authentication and basic CRUD
2. **Set up Auth0** (optional - can use Supabase Auth initially)
3. **Begin building features**: Products, factories, orders
4. **Add mobile app**: Flutter with the same backend

---

## 🔧 **Development Commands**

```bash
# Database operations
npm run db:generate    # Generate Prisma client
npm run db:push        # Push schema to Supabase
npm run db:studio      # Open Prisma Studio
npm run db:seed        # Seed development data

# Development
npm run dev:all        # Start all services
npm run dev:api        # API server only
npm run dev:web        # Web browser app only
npm run dev:mobile     # Mobile apps (Flutter Android & iOS)

# Testing
npm run test           # Run all tests
npm run test:api       # API tests only
npm run test:web       # Web tests only
npm run test:e2e       # End-to-end tests

# Production
npm run build          # Build all apps
npm run start          # Start production servers
npm run deploy:staging # Deploy to staging
```

---

## 🚨 **Troubleshooting**

### **Database Connection Issues**
```bash
# Test database connection
npx prisma db pull

# If connection fails, check:
# 1. Database password is correct
# 2. Supabase project is not paused
# 3. IP is whitelisted (Supabase allows all by default)
```

### **Environment Issues**
```bash
# Validate environment variables
npm run validate:env

# Check if all required variables are set
node -e "console.log(process.env.SUPABASE_URL)"
```

### **Docker Issues**
```bash
# Reset Docker if needed
docker-compose down
docker system prune -f
docker-compose up -d
```

---

## 📞 **Support**

- **Supabase Issues**: Check your project dashboard or Supabase docs
- **Development Issues**: Follow the troubleshooting guides in `DEVELOPMENT-SETUP.md`
- **Architecture Questions**: Review `FC-CHINA-TECHNICAL-DESIGN.md`

---

## 🎯 **What's Next?**

1. **Complete the setup above** (15 minutes)
2. **Start Phase 1 development** following `PHASE-1-IMPLEMENTATION-PLAN.md`
3. **Build your first feature** (authentication + basic product CRUD)
4. **Deploy to staging** using the same Supabase project

**🚀 Your FC-CHINA development environment is ready to go!**
