{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "outputs": []}, "lint:fix": {"cache": false, "outputs": []}, "type-check": {"dependsOn": ["^type-check"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"]}, "test:e2e": {"dependsOn": ["build"], "outputs": ["playwright-report/**", "test-results/**"]}, "clean": {"cache": false, "outputs": []}, "db:generate": {"cache": false, "outputs": []}, "db:push": {"cache": false, "outputs": []}, "db:migrate": {"cache": false, "outputs": []}, "db:studio": {"cache": false, "persistent": true}, "db:seed": {"cache": false, "outputs": []}, "validate:env": {"cache": false, "outputs": []}}}