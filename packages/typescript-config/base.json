{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "bundler", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "allowJs": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "noEmit": false, "incremental": true, "tsBuildInfoFile": ".tsbuildinfo"}, "exclude": ["node_modules"]}