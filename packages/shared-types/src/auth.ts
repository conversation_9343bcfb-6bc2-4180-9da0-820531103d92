import { z } from 'zod';

// Authentication types and schemas

export const UserRoleSchema = z.enum([
  'SUPER_ADMIN',
  'FACTORY_OWNER',
  'FACTORY_ADMIN',
  'FACTORY_MANAGER',
  'FACTORY_STAFF',
  'CUSTOMER',
  'CUSTOMER_ADMIN'
]);

export type UserRole = z.infer<typeof UserRoleSchema>;

export const UserStatusSchema = z.enum([
  'ACTIVE',
  'INACTIVE',
  'SUSPENDED',
  'PENDING_VERIFICATION'
]);

export type UserStatus = z.infer<typeof UserStatusSchema>;

// Permission system
export const PermissionSchema = z.enum([
  // System permissions
  'SYSTEM_ADMIN',
  'SYSTEM_READ',
  
  // Factory permissions
  'FACTORY_READ',
  'FACTORY_WRITE',
  'FACTORY_DELETE',
  'FACTORY_ADMIN',
  
  // Product permissions
  'PRODUCT_READ',
  'PRODUCT_WRITE',
  'PRODUCT_DELETE',
  'PRODUCT_PUBLISH',
  
  // Order permissions
  'ORDER_READ',
  'ORDER_WRITE',
  'ORDER_DELETE',
  'ORDER_PROCESS',
  
  // User permissions
  'USER_READ',
  'USER_WRITE',
  'USER_DELETE',
  'USER_INVITE',
  
  // Analytics permissions
  'ANALYTICS_READ',
  'ANALYTICS_EXPORT',
  
  // Message permissions
  'MESSAGE_READ',
  'MESSAGE_WRITE',
  'MESSAGE_DELETE',
]);

export type Permission = z.infer<typeof PermissionSchema>;

// Auth0 user profile
export interface Auth0Profile {
  sub: string;
  email: string;
  email_verified: boolean;
  name?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  locale?: string;
  updated_at?: string;
}

// JWT token payload
export interface JWTPayload {
  sub: string; // Auth0 user ID
  userId: string; // Our internal user ID
  email: string;
  role: UserRole;
  factoryId?: string;
  permissions: Permission[];
  iat: number;
  exp: number;
  aud: string;
  iss: string;
}

// Authentication context
export interface AuthContext {
  user: {
    id: string;
    auth0Id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    factoryId?: string;
    permissions: Permission[];
    avatar?: string;
  };
  token: string;
  isAuthenticated: boolean;
}

// Login/Register schemas
export const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  rememberMe: z.boolean().optional(),
});

export type LoginInput = z.infer<typeof LoginSchema>;

export const RegisterSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  role: UserRoleSchema,
  factoryId: z.string().optional(),
  phone: z.string().optional(),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms and conditions',
  }),
});

export type RegisterInput = z.infer<typeof RegisterSchema>;

// Password reset schemas
export const ForgotPasswordSchema = z.object({
  email: z.string().email(),
});

export type ForgotPasswordInput = z.infer<typeof ForgotPasswordSchema>;

export const ResetPasswordSchema = z.object({
  token: z.string(),
  password: z.string().min(8),
  confirmPassword: z.string().min(8),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export type ResetPasswordInput = z.infer<typeof ResetPasswordSchema>;

// Change password schema
export const ChangePasswordSchema = z.object({
  currentPassword: z.string(),
  newPassword: z.string().min(8),
  confirmPassword: z.string().min(8),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export type ChangePasswordInput = z.infer<typeof ChangePasswordSchema>;

// Session management
export interface Session {
  id: string;
  userId: string;
  token: string;
  refreshToken?: string;
  expiresAt: Date;
  createdAt: Date;
  lastAccessedAt: Date;
  ipAddress?: string;
  userAgent?: string;
}

// Authentication errors
export class AuthenticationError extends Error {
  constructor(message: string, public code: string = 'AUTH_ERROR') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  constructor(message: string, public code: string = 'AUTHORIZATION_ERROR') {
    super(message);
    this.name = 'AuthorizationError';
  }
}

// Role-based access control helpers
export const RoleHierarchy: Record<UserRole, number> = {
  SUPER_ADMIN: 100,
  FACTORY_OWNER: 80,
  FACTORY_ADMIN: 70,
  FACTORY_MANAGER: 60,
  FACTORY_STAFF: 50,
  CUSTOMER_ADMIN: 40,
  CUSTOMER: 30,
};

export const DefaultPermissions: Record<UserRole, Permission[]> = {
  SUPER_ADMIN: [
    'SYSTEM_ADMIN',
    'SYSTEM_READ',
    'FACTORY_READ',
    'FACTORY_WRITE',
    'FACTORY_DELETE',
    'FACTORY_ADMIN',
    'PRODUCT_READ',
    'PRODUCT_WRITE',
    'PRODUCT_DELETE',
    'PRODUCT_PUBLISH',
    'ORDER_READ',
    'ORDER_WRITE',
    'ORDER_DELETE',
    'ORDER_PROCESS',
    'USER_READ',
    'USER_WRITE',
    'USER_DELETE',
    'USER_INVITE',
    'ANALYTICS_READ',
    'ANALYTICS_EXPORT',
    'MESSAGE_READ',
    'MESSAGE_WRITE',
    'MESSAGE_DELETE',
  ],
  FACTORY_OWNER: [
    'FACTORY_READ',
    'FACTORY_WRITE',
    'FACTORY_ADMIN',
    'PRODUCT_READ',
    'PRODUCT_WRITE',
    'PRODUCT_DELETE',
    'PRODUCT_PUBLISH',
    'ORDER_READ',
    'ORDER_WRITE',
    'ORDER_PROCESS',
    'USER_READ',
    'USER_WRITE',
    'USER_INVITE',
    'ANALYTICS_READ',
    'ANALYTICS_EXPORT',
    'MESSAGE_READ',
    'MESSAGE_WRITE',
  ],
  FACTORY_ADMIN: [
    'FACTORY_READ',
    'FACTORY_WRITE',
    'PRODUCT_READ',
    'PRODUCT_WRITE',
    'PRODUCT_PUBLISH',
    'ORDER_READ',
    'ORDER_WRITE',
    'ORDER_PROCESS',
    'USER_READ',
    'USER_WRITE',
    'ANALYTICS_READ',
    'MESSAGE_READ',
    'MESSAGE_WRITE',
  ],
  FACTORY_MANAGER: [
    'FACTORY_READ',
    'PRODUCT_READ',
    'PRODUCT_WRITE',
    'ORDER_READ',
    'ORDER_WRITE',
    'ORDER_PROCESS',
    'USER_READ',
    'ANALYTICS_READ',
    'MESSAGE_READ',
    'MESSAGE_WRITE',
  ],
  FACTORY_STAFF: [
    'FACTORY_READ',
    'PRODUCT_READ',
    'ORDER_READ',
    'ORDER_WRITE',
    'MESSAGE_READ',
    'MESSAGE_WRITE',
  ],
  CUSTOMER_ADMIN: [
    'PRODUCT_READ',
    'ORDER_READ',
    'ORDER_WRITE',
    'USER_READ',
    'USER_WRITE',
    'MESSAGE_READ',
    'MESSAGE_WRITE',
  ],
  CUSTOMER: [
    'PRODUCT_READ',
    'ORDER_READ',
    'ORDER_WRITE',
    'MESSAGE_READ',
    'MESSAGE_WRITE',
  ],
};

// Helper functions
export function hasPermission(userPermissions: Permission[], requiredPermission: Permission): boolean {
  return userPermissions.includes(requiredPermission) || userPermissions.includes('SYSTEM_ADMIN');
}

export function hasAnyPermission(userPermissions: Permission[], requiredPermissions: Permission[]): boolean {
  return requiredPermissions.some(permission => hasPermission(userPermissions, permission));
}

export function hasAllPermissions(userPermissions: Permission[], requiredPermissions: Permission[]): boolean {
  return requiredPermissions.every(permission => hasPermission(userPermissions, permission));
}

export function canAccessFactory(userRole: UserRole, userFactoryId?: string, targetFactoryId?: string): boolean {
  // Super admin can access any factory
  if (userRole === 'SUPER_ADMIN') return true;
  
  // Customers can access any factory for browsing
  if (userRole === 'CUSTOMER' || userRole === 'CUSTOMER_ADMIN') return true;
  
  // Factory users can only access their own factory
  return userFactoryId === targetFactoryId;
}

export function getRoleLevel(role: UserRole): number {
  return RoleHierarchy[role] || 0;
}

export function canManageUser(managerRole: UserRole, targetRole: UserRole): boolean {
  return getRoleLevel(managerRole) > getRoleLevel(targetRole);
}
