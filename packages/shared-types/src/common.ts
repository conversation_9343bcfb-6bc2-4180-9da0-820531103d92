import { z } from 'zod';

// Common types used across the platform

export type UUID = string;

export type Timestamp = Date;

export interface BaseEntity {
  id: UUID;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface AuditableEntity extends BaseEntity {
  createdBy?: UUID;
  updatedBy?: UUID;
}

// Status enums
export const StatusSchema = z.enum(['ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED']);
export type Status = z.infer<typeof StatusSchema>;

export const VerificationStatusSchema = z.enum(['UNVERIFIED', 'PENDING', 'VERIFIED', 'REJECTED']);
export type VerificationStatus = z.infer<typeof VerificationStatusSchema>;

// Pagination
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// API Response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

// File upload
export interface FileUpload {
  id: UUID;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: Timestamp;
  uploadedBy: UUID;
}

// Address
export interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

// Contact information
export interface ContactInfo {
  email?: string;
  phone?: string;
  website?: string;
  socialMedia?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
  };
}

// Multi-language support
export interface LocalizedText {
  en: string;
  zh?: string;
  es?: string;
  fr?: string;
  de?: string;
  ja?: string;
  ko?: string;
}

// Currency and pricing
export const CurrencySchema = z.enum(['USD', 'EUR', 'GBP', 'CNY', 'JPY', 'KRW']);
export type Currency = z.infer<typeof CurrencySchema>;

export interface Price {
  amount: number;
  currency: Currency;
}

export interface PriceRange {
  min: Price;
  max: Price;
}

// Search and filtering
export interface SearchParams {
  query?: string;
  filters?: Record<string, any>;
  pagination?: PaginationParams;
}

export interface FilterOption {
  label: string;
  value: string;
  count?: number;
}

// Notification
export const NotificationTypeSchema = z.enum([
  'INFO',
  'SUCCESS', 
  'WARNING',
  'ERROR',
  'ORDER_UPDATE',
  'MESSAGE',
  'SYSTEM'
]);
export type NotificationType = z.infer<typeof NotificationTypeSchema>;

export interface Notification {
  id: UUID;
  type: NotificationType;
  title: string;
  message: string;
  read: boolean;
  userId: UUID;
  createdAt: Timestamp;
  data?: Record<string, any>;
}
