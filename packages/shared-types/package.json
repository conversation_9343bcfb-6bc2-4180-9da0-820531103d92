{"name": "@fc-china/shared-types", "version": "1.0.0", "description": "Shared TypeScript types for FC-CHINA", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts"}, "scripts": {"type-check": "tsc --noEmit", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "devDependencies": {"eslint": "^8.56.0", "typescript": "^5.3.3"}, "dependencies": {"zod": "^3.22.4"}}