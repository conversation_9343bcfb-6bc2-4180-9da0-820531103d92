{"name": "@fc-china/ui", "version": "1.0.0", "description": "Shared React UI components for FC-CHINA", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./styles": "./src/styles.css"}, "scripts": {"type-check": "tsc --noEmit", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint src/**/*.{ts,tsx} --fix", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4", "@radix-ui/react-slot": "^1.1.0", "lucide-react": "^0.460.0"}, "devDependencies": {"@types/react": "^19", "@types/react-dom": "^19", "typescript": "^5.3.3", "eslint": "^8.56.0", "tailwindcss": "^4"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}}