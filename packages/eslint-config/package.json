{"name": "@fc-china/eslint-config", "version": "1.0.0", "description": "Shared ESLint configuration for FC-CHINA", "main": "index.js", "files": ["index.js", "next.js", "react.js"], "dependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint-config-next": "^14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2"}, "peerDependencies": {"eslint": "^8.0.0", "typescript": "^5.0.0"}}