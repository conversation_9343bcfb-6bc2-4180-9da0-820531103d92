# FC-CHINA UI/UX Design System

## 📋 **Document Information**
- **Project**: FC-CHINA Multi-Tenant Factory Admin Platform
- **Version**: 1.0
- **Last Updated**: December 2024
- **Document Type**: UI/UX Design System
- **Status**: Draft

---

## 🎨 **Design Philosophy**

### **Platform-Specific Design Strategy**
- **🖥️ Web Application**: Desktop-first design for factory users (complex workflows, data management)
- **📱 Mobile Application**: Mobile-first design for customer users (product browsing, quick communication)

### **Core Principles**

1. **Industrial Elegance**: Clean, professional design that reflects manufacturing precision
2. **Platform-Optimized UX**: Desktop workflows for factories, mobile workflows for customers
3. **Multi-Tenant Flexibility**: Consistent experience across different factory brands
4. **Dual-Platform Design**: Web desktop-first, mobile app mobile-first
5. **Accessibility**: WCAG 2.1 AA compliance for inclusive design
6. **Performance**: Lightweight components optimized per platform
7. **Scalability**: Modular system that grows with the platform

### **Accessibility Testing Procedures**

#### **Automated Testing**
- **axe-core**: Automated accessibility testing in Jest and Cypress
- **Lighthouse CI**: Accessibility audits in CI/CD pipeline
- **Pa11y**: Command-line accessibility testing
- **WAVE**: Web accessibility evaluation during development

#### **Manual Testing Checklist**
- **Keyboard Navigation**: All interactive elements accessible via keyboard
- **Screen Reader Testing**: VoiceOver (macOS), NVDA (Windows), TalkBack (Android)
- **Color Contrast**: Minimum 4.5:1 ratio for normal text, 3:1 for large text
- **Focus Management**: Visible focus indicators and logical tab order
- **Alternative Text**: Descriptive alt text for all images
- **Form Labels**: Proper labeling and error messaging
- **Semantic HTML**: Correct use of headings, landmarks, and ARIA attributes

#### **Testing Schedule**
- **Daily**: Automated accessibility tests in CI/CD
- **Weekly**: Manual keyboard navigation testing
- **Monthly**: Comprehensive screen reader testing
- **Quarterly**: Full WCAG 2.1 AA compliance audit

### **Component Testing Strategy**

#### **Unit Testing (Jest + React Testing Library)**
```typescript
// Component test example
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading state', () => {
    render(<Button loading>Click me</Button>);
    expect(screen.getByRole('button')).toHaveAttribute('disabled');
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('meets accessibility requirements', async () => {
    const { container } = render(<Button>Click me</Button>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

#### **Visual Regression Testing (Chromatic)**
- **Storybook Integration**: All components documented in Storybook
- **Visual Diff Detection**: Automated screenshot comparison
- **Cross-browser Testing**: Chrome, Firefox, Safari, Edge
- **Responsive Testing**: Mobile, tablet, desktop viewports

#### **Integration Testing (Cypress)**
```typescript
// Integration test example
describe('Product Card Integration', () => {
  it('displays product information correctly', () => {
    cy.visit('/products');
    cy.get('[data-testid="product-card"]').first().within(() => {
      cy.get('[data-testid="product-name"]').should('be.visible');
      cy.get('[data-testid="product-price"]').should('be.visible');
      cy.get('[data-testid="product-image"]').should('be.visible');
    });
  });

  it('handles user interactions', () => {
    cy.get('[data-testid="product-card"]').first().click();
    cy.url().should('include', '/products/');
    cy.get('[data-testid="product-details"]').should('be.visible');
  });
});
```

### **Design Values**

- **Clarity**: Information hierarchy that guides users naturally
- **Efficiency**: Streamlined workflows for busy factory environments
- **Trust**: Professional appearance that builds customer confidence
- **Adaptability**: Flexible components for diverse factory needs

### **Multi-Tenant Brand Customization**

#### **Factory Branding System**
```css
/* Factory-specific CSS custom properties */
:root[data-factory-id="factory-123"] {
  --brand-primary: #1a365d;
  --brand-secondary: #2d3748;
  --brand-accent: #ed8936;
  --brand-logo-url: url('/uploads/factory-123/logo.png');
  --brand-font-primary: 'Custom Factory Font', sans-serif;
}

/* Dynamic brand application */
.factory-header {
  background: var(--brand-primary);
  color: white;
}

.factory-logo {
  background-image: var(--brand-logo-url);
  background-size: contain;
  background-repeat: no-repeat;
}

.brand-button {
  background: var(--brand-accent);
  border-color: var(--brand-accent);
}
```

#### **Brand Customization API**
```typescript
interface FactoryBrandConfig {
  factoryId: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  logo: {
    url: string;
    width: number;
    height: number;
  };
  typography: {
    primaryFont?: string;
    headingFont?: string;
  };
  customCSS?: string;
}

// Brand configuration management
export class BrandManager {
  static async applyFactoryBranding(factoryId: string, config: FactoryBrandConfig) {
    // Generate CSS custom properties
    const cssVariables = this.generateCSSVariables(config);

    // Inject into document head
    const styleElement = document.createElement('style');
    styleElement.id = `factory-branding-${factoryId}`;
    styleElement.textContent = cssVariables;
    document.head.appendChild(styleElement);
  }

  static generateCSSVariables(config: FactoryBrandConfig): string {
    return `
      :root[data-factory-id="${config.factoryId}"] {
        --brand-primary: ${config.colors.primary};
        --brand-secondary: ${config.colors.secondary};
        --brand-accent: ${config.colors.accent};
        --brand-logo-url: url('${config.logo.url}');
        ${config.typography.primaryFont ? `--brand-font-primary: '${config.typography.primaryFont}', sans-serif;` : ''}
        ${config.typography.headingFont ? `--brand-font-heading: '${config.typography.headingFont}', sans-serif;` : ''}
      }
      ${config.customCSS || ''}
    `;
  }
}
```

### **Component Performance Optimization**

#### **Performance Best Practices**
```typescript
// Lazy loading for heavy components
const ProductGallery = lazy(() => import('./ProductGallery'));
const AnalyticsDashboard = lazy(() => import('./AnalyticsDashboard'));

// Memoization for expensive calculations
const ProductCard = memo(({ product, onView, onContact }) => {
  const formattedPrice = useMemo(() => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: product.currency
    }).format(product.basePrice);
  }, [product.basePrice, product.currency]);

  return (
    <div className="product-card">
      {/* Component content */}
    </div>
  );
});

// Virtual scrolling for large lists
const ProductList = ({ products }) => {
  const { virtualItems, totalSize, scrollElementRef } = useVirtualizer({
    count: products.length,
    getScrollElement: () => scrollElementRef.current,
    estimateSize: () => 200,
    overscan: 5
  });

  return (
    <div ref={scrollElementRef} style={{ height: '400px', overflow: 'auto' }}>
      <div style={{ height: totalSize, position: 'relative' }}>
        {virtualItems.map((virtualItem) => (
          <div
            key={virtualItem.index}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: virtualItem.size,
              transform: `translateY(${virtualItem.start}px)`
            }}
          >
            <ProductCard product={products[virtualItem.index]} />
          </div>
        ))}
      </div>
    </div>
  );
};
```

#### **Bundle Optimization**
```typescript
// Code splitting by route
const FactoryDashboard = lazy(() => import('../pages/FactoryDashboard'));
const CustomerMarketplace = lazy(() => import('../pages/CustomerMarketplace'));

// Dynamic imports for features
const loadAnalytics = () => import('../features/analytics');
const loadMessaging = () => import('../features/messaging');

// Tree shaking optimization
export { Button, Input, Card } from './components'; // Named exports only
```

#### **Image Optimization**
```typescript
// Next.js Image component with optimization
import Image from 'next/image';

const OptimizedProductImage = ({ src, alt, ...props }) => {
  return (
    <Image
      src={src}
      alt={alt}
      width={400}
      height={300}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      {...props}
    />
  );
};

// Progressive image loading
const ProgressiveImage = ({ src, placeholder, alt }) => {
  const [imageLoaded, setImageLoaded] = useState(false);

  return (
    <div className="relative">
      <img
        src={placeholder}
        alt={alt}
        className={`transition-opacity duration-300 ${imageLoaded ? 'opacity-0' : 'opacity-100'}`}
      />
      <img
        src={src}
        alt={alt}
        onLoad={() => setImageLoaded(true)}
        className={`absolute inset-0 transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
      />
    </div>
  );
};
```

---

## 🎯 **User Experience Strategy**

### **User Journey Mapping**

#### **Factory Admin Journey**
```
1. Onboarding → 2. Setup → 3. Product Management → 4. Order Processing → 5. Customer Communication
   ↓              ↓         ↓                    ↓                  ↓
- Welcome       - Profile  - Add Products       - Review Orders    - Message Customers
- Verification  - Branding - Organize Catalog   - Update Status    - Send Quotes
- Tutorial      - Settings - Set Pricing        - Track Progress   - Handle Inquiries
```

#### **Customer Journey**
```
1. Discovery → 2. Exploration → 3. Inquiry → 4. Ordering → 5. Tracking
   ↓            ↓               ↓           ↓            ↓
- Browse       - View Products  - Contact   - Place      - Monitor
- Search       - Compare        - Request   - Order      - Progress
- Filter       - Details        - Quote     - Payment    - Delivery
```

### **Information Architecture**

```
FC-CHINA Platform
├── Factory Portal
│   ├── Dashboard
│   │   ├── Overview Metrics
│   │   ├── Recent Orders
│   │   ├── Messages
│   │   └── Quick Actions
│   ├── Products
│   │   ├── Product List
│   │   ├── Categories
│   │   ├── Add/Edit Product
│   │   └── Inventory
│   ├── Orders
│   │   ├── Order List
│   │   ├── Order Details
│   │   ├── Status Updates
│   │   └── Shipping
│   ├── Customers
│   │   ├── Customer List
│   │   ├── Messages
│   │   ├── Quotes
│   │   └── History
│   └── Settings
│       ├── Factory Profile
│       ├── Branding
│       ├── Users
│       └── Preferences
└── Customer Portal
    ├── Marketplace
    │   ├── Factory Directory
    │   ├── Product Catalog
    │   ├── Search & Filter
    │   └── Product Details
    ├── Communication
    │   ├── Messages
    │   ├── Quotes
    │   └── Support
    ├── Orders
    │   ├── Order History
    │   ├── Tracking
    │   └── Returns
    └── Account
        ├── Profile
        ├── Preferences
        └── Security
```

---

## 🎨 **Visual Design System**

### **Color Palette**

#### **Primary Colors**
```css
/* Industrial Blue - Trust, Reliability */
--primary-50: #eff6ff;
--primary-100: #dbeafe;
--primary-200: #bfdbfe;
--primary-300: #93c5fd;
--primary-400: #60a5fa;
--primary-500: #3b82f6;  /* Main Primary */
--primary-600: #2563eb;
--primary-700: #1d4ed8;
--primary-800: #1e40af;
--primary-900: #1e3a8a;
--primary-950: #172554;

/* Dark Mode Color Overrides */
[data-theme="dark"] {
  --primary-50: #172554;
  --primary-100: #1e3a8a;
  --primary-200: #1e40af;
  --primary-300: #1d4ed8;
  --primary-400: #2563eb;
  --primary-500: #3b82f6;
  --primary-600: #60a5fa;
  --primary-700: #93c5fd;
  --primary-800: #bfdbfe;
  --primary-900: #dbeafe;
  --primary-950: #eff6ff;
}

/* Dark Mode Implementation */
.dark-mode-toggle {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  background: var(--neutral-800);
  color: var(--neutral-100);
  border: none;
  border-radius: var(--radius-full);
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.dark-mode-toggle:hover {
  background: var(--neutral-700);
  transform: scale(1.05);
}

/* Dark mode switching mechanism */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* System preference detection */
.theme-system {
  --theme-preference: system;
}

.theme-light {
  --theme-preference: light;
}

.theme-dark {
  --theme-preference: dark;
}
```

#### **Secondary Colors**
```css
/* Manufacturing Orange - Energy, Action */
--secondary-50: #fff7ed;
--secondary-100: #ffedd5;
--secondary-200: #fed7aa;
--secondary-300: #fdba74;
--secondary-400: #fb923c;
--secondary-500: #f97316;  /* Main Secondary */
--secondary-600: #ea580c;
--secondary-700: #c2410c;
--secondary-800: #9a3412;
--secondary-900: #7c2d12;
--secondary-950: #431407;
```

#### **Neutral Colors**
```css
/* Professional Grays */
--neutral-50: #f8fafc;
--neutral-100: #f1f5f9;
--neutral-200: #e2e8f0;
--neutral-300: #cbd5e1;
--neutral-400: #94a3b8;
--neutral-500: #64748b;
--neutral-600: #475569;
--neutral-700: #334155;
--neutral-800: #1e293b;
--neutral-900: #0f172a;
--neutral-950: #020617;
```

#### **Status Colors**
```css
/* Success - Green */
--success-50: #f0fdf4;
--success-500: #22c55e;
--success-600: #16a34a;
--success-700: #15803d;

/* Warning - Amber */
--warning-50: #fffbeb;
--warning-500: #f59e0b;
--warning-600: #d97706;
--warning-700: #b45309;

/* Error - Red */
--error-50: #fef2f2;
--error-500: #ef4444;
--error-600: #dc2626;
--error-700: #b91c1c;

/* Info - Blue */
--info-50: #eff6ff;
--info-500: #3b82f6;
--info-600: #2563eb;
--info-700: #1d4ed8;
```

### **Typography**

#### **Font Stack**
```css
/* Primary Font - Inter (Clean, Modern) */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

/* Monospace Font - JetBrains Mono (Code, Data) */
--font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

/* Display Font - Poppins (Headers, Branding) */
--font-display: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;

/* Icon System */
--icon-library: 'Heroicons'; /* Primary icon library */
--icon-size-xs: 0.75rem;     /* 12px */
--icon-size-sm: 1rem;        /* 16px */
--icon-size-md: 1.25rem;     /* 20px */
--icon-size-lg: 1.5rem;      /* 24px */
--icon-size-xl: 2rem;        /* 32px */
--icon-size-2xl: 2.5rem;     /* 40px */
```

#### **Type Scale**
```css
/* Display Sizes */
--text-display-2xl: 4.5rem;   /* 72px */
--text-display-xl: 3.75rem;   /* 60px */
--text-display-lg: 3rem;      /* 48px */
--text-display-md: 2.25rem;   /* 36px */
--text-display-sm: 1.875rem;  /* 30px */

/* Heading Sizes */
--text-h1: 2.25rem;   /* 36px */
--text-h2: 1.875rem;  /* 30px */
--text-h3: 1.5rem;    /* 24px */
--text-h4: 1.25rem;   /* 20px */
--text-h5: 1.125rem;  /* 18px */
--text-h6: 1rem;      /* 16px */

/* Body Sizes */
--text-xl: 1.25rem;   /* 20px */
--text-lg: 1.125rem;  /* 18px */
--text-base: 1rem;    /* 16px */
--text-sm: 0.875rem;  /* 14px */
--text-xs: 0.75rem;   /* 12px */

/* Line Heights */
--leading-tight: 1.25;
--leading-normal: 1.5;
--leading-relaxed: 1.75;

/* Font Weights */
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
--font-extrabold: 800;
```

### **Spacing System**

```css
/* 8px Base Unit System */
--space-0: 0;
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-5: 1.25rem;   /* 20px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-10: 2.5rem;   /* 40px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
--space-20: 5rem;     /* 80px */
--space-24: 6rem;     /* 96px */
--space-32: 8rem;     /* 128px */
```

### **Border Radius**

```css
--radius-none: 0;
--radius-sm: 0.125rem;   /* 2px */
--radius-base: 0.25rem;  /* 4px */
--radius-md: 0.375rem;   /* 6px */
--radius-lg: 0.5rem;     /* 8px */
--radius-xl: 0.75rem;    /* 12px */
--radius-2xl: 1rem;      /* 16px */
--radius-3xl: 1.5rem;    /* 24px */
--radius-full: 9999px;
```

### **Shadows**

```css
/* Elevation System */
--shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
--shadow-base: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
--shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
--shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
--shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
--shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

/* Colored Shadows */
--shadow-primary: 0 10px 15px -3px rgb(59 130 246 / 0.1);
--shadow-secondary: 0 10px 15px -3px rgb(249 115 22 / 0.1);
```

### **Animation System**

```css
/* Timing Functions */
--ease-linear: linear;
--ease-in: cubic-bezier(0.4, 0, 1, 1);
--ease-out: cubic-bezier(0, 0, 0.2, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
--ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

/* Duration */
--duration-75: 75ms;
--duration-100: 100ms;
--duration-150: 150ms;
--duration-200: 200ms;
--duration-300: 300ms;
--duration-500: 500ms;
--duration-700: 700ms;
--duration-1000: 1000ms;

/* Common Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
```

### **Component States**

```css
/* Interactive States */
.state-default {
  transition: all var(--duration-200) var(--ease-out);
}

.state-hover:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.state-active:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.state-focus:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.state-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.state-loading {
  position: relative;
  color: transparent;
}

.state-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin var(--duration-1000) linear infinite;
}
```

---

## 📊 **Data Visualization Guidelines**

### **Chart Color Palette**

```css
/* Data Visualization Colors */
--chart-primary: #3b82f6;
--chart-secondary: #f97316;
--chart-success: #22c55e;
--chart-warning: #f59e0b;
--chart-error: #ef4444;
--chart-info: #06b6d4;
--chart-purple: #8b5cf6;
--chart-pink: #ec4899;
--chart-indigo: #6366f1;
--chart-teal: #14b8a6;

/* Multi-series palette */
--chart-series-1: #3b82f6;
--chart-series-2: #f97316;
--chart-series-3: #22c55e;
--chart-series-4: #f59e0b;
--chart-series-5: #ef4444;
--chart-series-6: #8b5cf6;
--chart-series-7: #06b6d4;
--chart-series-8: #ec4899;
```

### **Chart Components**

#### **Dashboard Metrics Card**
```tsx
interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period: string;
  };
  icon?: React.ReactNode;
  trend?: Array<{ x: string; y: number }>;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  trend
}) => {
  return (
    <div className="bg-white rounded-lg border border-neutral-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-neutral-600">{title}</p>
          <p className="text-2xl font-bold text-neutral-900 mt-1">{value}</p>
          {change && (
            <div className={`flex items-center mt-2 text-sm ${
              change.type === 'increase' ? 'text-success-600' : 'text-error-600'
            }`}>
              {change.type === 'increase' ? (
                <ArrowUpIcon className="w-4 h-4 mr-1" />
              ) : (
                <ArrowDownIcon className="w-4 h-4 mr-1" />
              )}
              <span>{Math.abs(change.value)}% from {change.period}</span>
            </div>
          )}
        </div>
        {icon && (
          <div className="p-3 bg-primary-50 rounded-lg">
            <div className="text-primary-600">{icon}</div>
          </div>
        )}
      </div>
      {trend && (
        <div className="mt-4 h-16">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={trend}>
              <Line
                type="monotone"
                dataKey="y"
                stroke="var(--chart-primary)"
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      )}
    </div>
  );
};
```

### **Table Components**

#### **Data Table**
```tsx
interface Column<T> {
  key: keyof T;
  title: string;
  width?: string;
  sortable?: boolean;
  render?: (value: any, record: T) => React.ReactNode;
}

interface DataTableProps<T> {
  columns: Column<T>[];
  data: T[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  onSort?: (key: keyof T, direction: 'asc' | 'desc') => void;
}

const DataTable = <T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  pagination,
  onSort
}: DataTableProps<T>) => {
  const [sortKey, setSortKey] = useState<keyof T | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSort = (key: keyof T) => {
    if (!onSort) return;
    
    const newDirection = sortKey === key && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortKey(key);
    setSortDirection(newDirection);
    onSort(key, newDirection);
  };

  return (
    <div className="bg-white rounded-lg border border-neutral-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-neutral-200">
          <thead className="bg-neutral-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={String(column.key)}
                  className={`px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider ${
                    column.sortable ? 'cursor-pointer hover:bg-neutral-100' : ''
                  }`}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.title}</span>
                    {column.sortable && (
                      <div className="flex flex-col">
                        <ChevronUpIcon className={`w-3 h-3 ${
                          sortKey === column.key && sortDirection === 'asc'
                            ? 'text-primary-600'
                            : 'text-neutral-300'
                        }`} />
                        <ChevronDownIcon className={`w-3 h-3 -mt-1 ${
                          sortKey === column.key && sortDirection === 'desc'
                            ? 'text-primary-600'
                            : 'text-neutral-300'
                        }`} />
                      </div>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-neutral-200">
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-12 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span className="ml-2 text-neutral-500">Loading...</span>
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-12 text-center text-neutral-500">
                  No data available
                </td>
              </tr>
            ) : (
              data.map((record, index) => (
                <tr key={index} className="hover:bg-neutral-50">
                  {columns.map((column) => (
                    <td key={String(column.key)} className="px-6 py-4 whitespace-nowrap text-sm text-neutral-900">
                      {column.render
                        ? column.render(record[column.key], record)
                        : String(record[column.key])}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {pagination && (
        <div className="bg-white px-4 py-3 border-t border-neutral-200 sm:px-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 flex justify-between sm:hidden">
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.current === 1}
                onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.current * pagination.pageSize >= pagination.total}
                onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
              >
                Next
              </Button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-neutral-700">
                  Showing{' '}
                  <span className="font-medium">
                    {(pagination.current - 1) * pagination.pageSize + 1}
                  </span>{' '}
                  to{' '}
                  <span className="font-medium">
                    {Math.min(pagination.current * pagination.pageSize, pagination.total)}
                  </span>{' '}
                  of{' '}
                  <span className="font-medium">{pagination.total}</span>{' '}
                  results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {/* Pagination controls */}
                </nav>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
```

---

## 📐 **Grid System & Layout**

### **Responsive Grid**

```css
/* Container Sizes */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container { max-width: 640px; }
}

@media (min-width: 768px) {
  .container { max-width: 768px; }
}

@media (min-width: 1024px) {
  .container { max-width: 1024px; }
}

@media (min-width: 1280px) {
  .container { max-width: 1280px; }
}

@media (min-width: 1536px) {
  .container { max-width: 1536px; }
}

/* Grid System */
.grid {
  display: grid;
  gap: 1rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

/* Responsive Grid */
@media (min-width: 640px) {
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 768px) {
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
}
```

### **Layout Components**

```tsx
// Sidebar Layout
const SidebarLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div className="flex h-screen bg-neutral-50">
      <aside className="w-64 bg-white border-r border-neutral-200">
        {/* Sidebar content */}
      </aside>
      <main className="flex-1 overflow-auto">
        {children}
      </main>
    </div>
  );
};

// Dashboard Grid
const DashboardGrid: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {children}
    </div>
  );
};
```

---

## ✅ **Form Validation Patterns**

### **Validation States**

```css
/* Form Validation Styles */
.form-field {
  position: relative;
  margin-bottom: 1rem;
}

.form-field.error .form-input {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-field.success .form-input {
  border-color: var(--success-500);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-field.warning .form-input {
  border-color: var(--warning-500);
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.form-message {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.form-message.error {
  color: var(--error-600);
}

.form-message.success {
  color: var(--success-600);
}

.form-message.warning {
  color: var(--warning-600);
}
```

### **Real-time Validation**

```tsx
interface FormFieldProps {
  label: string;
  name: string;
  type?: string;
  placeholder?: string;
  required?: boolean;
  error?: string;
  success?: string;
  warning?: string;
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  validation?: (value: string) => { valid: boolean; message?: string };
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  type = 'text',
  placeholder,
  required = false,
  error,
  success,
  warning,
  value,
  onChange,
  onBlur,
  validation
}) => {
  const [touched, setTouched] = useState(false);
  const [validationState, setValidationState] = useState<{
    valid: boolean;
    message?: string;
  }>({ valid: true });

  useEffect(() => {
    if (validation && touched && value) {
      const result = validation(value);
      setValidationState(result);
    }
  }, [value, validation, touched]);

  const handleBlur = () => {
    setTouched(true);
    onBlur?.();
  };

  const getValidationState = () => {
    if (error) return 'error';
    if (success) return 'success';
    if (warning) return 'warning';
    if (touched && !validationState.valid) return 'error';
    if (touched && validationState.valid && value) return 'success';
    return '';
  };

  const currentState = getValidationState();
  const message = error || success || warning || (touched ? validationState.message : '');

  return (
    <div className={`form-field ${currentState}`}>
      <label className="form-label" htmlFor={name}>
        {label}
        {required && <span className="text-error-500 ml-1">*</span>}
      </label>
      <input
        id={name}
        name={name}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onBlur={handleBlur}
        className="form-input"
        aria-invalid={currentState === 'error'}
        aria-describedby={message ? `${name}-message` : undefined}
      />
      {message && (
        <div id={`${name}-message`} className={`form-message ${currentState}`}>
          <div className="flex items-center">
            {currentState === 'error' && <ExclamationCircleIcon className="w-4 h-4 mr-1" />}
            {currentState === 'success' && <CheckCircleIcon className="w-4 h-4 mr-1" />}
            {currentState === 'warning' && <ExclamationTriangleIcon className="w-4 h-4 mr-1" />}
            <span>{message}</span>
          </div>
        </div>
      )}
    </div>
  );
};
```

---

## 📱 **Mobile Gesture Patterns**

### **Touch Interactions**

```css
/* Touch-friendly sizing */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Swipe gestures */
.swipeable {
  touch-action: pan-x;
  user-select: none;
}

.swipe-left {
  transform: translateX(-100%);
  transition: transform 0.3s ease-out;
}

.swipe-right {
  transform: translateX(100%);
  transition: transform 0.3s ease-out;
}

/* Pull-to-refresh */
.pull-to-refresh {
  position: relative;
  overflow: hidden;
}

.pull-indicator {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  transition: top 0.3s ease-out;
}

.pull-indicator.active {
  top: 20px;
}
```

### **Gesture Components**

```tsx
// Swipeable Card Component
interface SwipeableCardProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  threshold?: number;
}

const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  threshold = 100
}) => {
  const [startX, setStartX] = useState(0);
  const [currentX, setCurrentX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  const handleTouchStart = (e: React.TouchEvent) => {
    setStartX(e.touches[0].clientX);
    setIsDragging(true);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    setCurrentX(e.touches[0].clientX - startX);
  };

  const handleTouchEnd = () => {
    if (!isDragging) return;
    
    if (Math.abs(currentX) > threshold) {
      if (currentX > 0 && onSwipeRight) {
        onSwipeRight();
      } else if (currentX < 0 && onSwipeLeft) {
        onSwipeLeft();
      }
    }
    
    setCurrentX(0);
    setIsDragging(false);
  };

  return (
    <div
      className="swipeable"
      style={{
        transform: `translateX(${currentX}px)`,
        transition: isDragging ? 'none' : 'transform 0.3s ease-out'
      }}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {children}
    </div>
  );
};

// Pull to Refresh Component
const PullToRefresh: React.FC<{
  children: React.ReactNode;
  onRefresh: () => Promise<void>;
}> = ({ children, onRefresh }) => {
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [startY, setStartY] = useState(0);

  const handleTouchStart = (e: React.TouchEvent) => {
    setStartY(e.touches[0].clientY);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    const currentY = e.touches[0].clientY;
    const distance = Math.max(0, currentY - startY);
    setPullDistance(Math.min(distance, 120));
  };

  const handleTouchEnd = async () => {
    if (pullDistance > 60 && !isRefreshing) {
      setIsRefreshing(true);
      await onRefresh();
      setIsRefreshing(false);
    }
    setPullDistance(0);
  };

  return (
    <div
      className="pull-to-refresh"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <div
        className={`pull-indicator ${pullDistance > 60 ? 'active' : ''}`}
        style={{ top: `${pullDistance - 60}px` }}
      >
        {isRefreshing ? (
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600" />
        ) : (
          <ChevronDownIcon className="w-6 h-6 text-neutral-400" />
        )}
      </div>
      <div style={{ transform: `translateY(${pullDistance}px)` }}>
        {children}
      </div>
    </div>
  );
};
```

---

## 🌐 **PWA Design Considerations**

### **App Shell Architecture**

```css
/* App Shell Styles */
.app-shell {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.app-header {
  flex-shrink: 0;
  background: var(--primary-600);
  color: white;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.app-navigation {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid var(--neutral-200);
  padding: 0.5rem;
}

/* Offline indicator */
.offline-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--warning-500);
  color: white;
  text-align: center;
  padding: 0.5rem;
  transform: translateY(-100%);
  transition: transform 0.3s ease-out;
  z-index: 1000;
}

.offline-indicator.show {
  transform: translateY(0);
}
```

### **PWA Components**

```tsx
// Install Prompt Component
const InstallPrompt: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [showInstall, setShowInstall] = useState(false);

  useEffect(() => {
    const handler = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setShowInstall(true);
    };

    window.addEventListener('beforeinstallprompt', handler);
    return () => window.removeEventListener('beforeinstallprompt', handler);
  }, []);

  const handleInstall = async () => {
    if (!deferredPrompt) return;
    
    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      setShowInstall(false);
    }
    
    setDeferredPrompt(null);
  };

  if (!showInstall) return null;

  return (
    <div className="fixed bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg border border-neutral-200 p-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-medium text-neutral-900">Install FC-CHINA</h3>
          <p className="text-sm text-neutral-600">Get the full app experience</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" onClick={() => setShowInstall(false)}>
            Later
          </Button>
          <Button size="sm" onClick={handleInstall}>
            Install
          </Button>
        </div>
      </div>
    </div>
  );
};

// Offline Status Component
const OfflineStatus: React.FC = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <div className={`offline-indicator ${!isOnline ? 'show' : ''}`}>
      <div className="flex items-center justify-center space-x-2">
        <WifiIcon className="w-4 h-4" />
        <span>You're currently offline. Some features may be limited.</span>
      </div>
    </div>
  );
};
```

### **Print Styles**

```css
/* Print-specific styles */
@media print {
  /* Hide non-essential elements */
  .no-print,
  .app-navigation,
  .floating-action-button,
  .sidebar {
    display: none !important;
  }
  
  /* Optimize layout for print */
  .app-content {
    overflow: visible;
    height: auto;
  }
  
  /* Ensure good contrast */
  * {
    color: black !important;
    background: white !important;
  }
  
  /* Page breaks */
  .page-break {
    page-break-before: always;
  }
  
  .avoid-break {
    page-break-inside: avoid;
  }
  
  /* Table styles */
  table {
    border-collapse: collapse;
    width: 100%;
  }
  
  th, td {
    border: 1px solid black;
    padding: 8px;
    text-align: left;
  }
  
  /* Link handling */
  a[href]:after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
  }
}
```

---

## 🧩 **Component Library**

### **Button Components**

#### **Primary Button**
```tsx
// React Component
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  children: React.ReactNode;
  onClick?: () => void;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  icon,
  iconPosition = 'left',
  children,
  onClick,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';
  
  const variantClasses = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',
    secondary: 'bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500',
    outline: 'border border-neutral-300 bg-white text-neutral-700 hover:bg-neutral-50 focus:ring-primary-500',
    ghost: 'text-neutral-700 hover:bg-neutral-100 focus:ring-primary-500',
    destructive: 'bg-error-600 text-white hover:bg-error-700 focus:ring-error-500'
  };
  
  const sizeClasses = {
    xs: 'px-2.5 py-1.5 text-xs rounded-md',
    sm: 'px-3 py-2 text-sm rounded-md',
    md: 'px-4 py-2 text-sm rounded-md',
    lg: 'px-4 py-2 text-base rounded-md',
    xl: 'px-6 py-3 text-base rounded-md'
  };
  
  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      )}
      {icon && iconPosition === 'left' && !loading && (
        <span className="mr-2">{icon}</span>
      )}
      {children}
      {icon && iconPosition === 'right' && !loading && (
        <span className="ml-2">{icon}</span>
      )}
    </button>
  );
};
```

#### **Flutter Button**
```dart
// Flutter Component
class FCButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final FCButtonVariant variant;
  final FCButtonSize size;
  final bool loading;
  final bool disabled;
  final Widget? icon;
  final IconPosition iconPosition;
  
  const FCButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.variant = FCButtonVariant.primary,
    this.size = FCButtonSize.medium,
    this.loading = false,
    this.disabled = false,
    this.icon,
    this.iconPosition = IconPosition.left,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = _getColors(theme);
    final padding = _getPadding();
    final textStyle = _getTextStyle(theme);
    
    return ElevatedButton(
      onPressed: (disabled || loading) ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: colors.background,
        foregroundColor: colors.foreground,
        padding: padding,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
        elevation: variant == FCButtonVariant.outline ? 0 : 2,
      ),
      child: loading
          ? SizedBox(
              height: 16,
              width: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(colors.foreground),
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null && iconPosition == IconPosition.left) ..[
                  icon!,
                  const SizedBox(width: 8),
                ],
                Text(text, style: textStyle),
                if (icon != null && iconPosition == IconPosition.right) ..[
                  const SizedBox(width: 8),
                  icon!,
                ],
              ],
            ),
    );
  }
}
```

### **Input Components**

#### **Text Input**
```tsx
// React Text Input
interface TextInputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

const TextInput: React.FC<TextInputProps> = ({
  label,
  placeholder,
  value,
  onChange,
  error,
  helperText,
  required = false,
  disabled = false,
  type = 'text',
  icon,
  iconPosition = 'left',
  ...props
}) => {
  const inputId = useId();
  const hasError = !!error;
  
  return (
    <div className="space-y-1">
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-medium text-neutral-700"
        >
          {label}
          {required && <span className="text-error-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        {icon && iconPosition === 'left' && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-neutral-400">{icon}</span>
          </div>
        )}
        
        <input
          id={inputId}
          type={type}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className={`
            block w-full px-3 py-2 border rounded-md shadow-sm placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-offset-0 sm:text-sm
            ${icon && iconPosition === 'left' ? 'pl-10' : ''}
            ${icon && iconPosition === 'right' ? 'pr-10' : ''}
            ${hasError
              ? 'border-error-300 focus:border-error-500 focus:ring-error-500'
              : 'border-neutral-300 focus:border-primary-500 focus:ring-primary-500'
            }
            ${disabled ? 'bg-neutral-50 text-neutral-500 cursor-not-allowed' : 'bg-white'}
          `}
          {...props}
        />
        
        {icon && iconPosition === 'right' && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className="text-neutral-400">{icon}</span>
          </div>
        )}
      </div>
      
      {(error || helperText) && (
        <p className={`text-xs ${
          hasError ? 'text-error-600' : 'text-neutral-500'
        }`}>
          {error || helperText}
        </p>
      )}
    </div>
  );
};
```

### **Card Components**

#### **Product Card**
```tsx
// React Product Card
interface ProductCardProps {
  product: {
    id: string;
    name: string;
    description?: string;
    basePrice: number;
    currency: string;
    images: string[];
    category: { name: string };
    factory: { name: string; logo?: string };
  };
  onView: (productId: string) => void;
  onContact: (productId: string) => void;
  variant?: 'default' | 'compact';
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onView,
  onContact,
  variant = 'default'
}) => {
  const isCompact = variant === 'compact';
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 hover:shadow-md transition-shadow duration-200">
      {/* Product Image */}
      <div className={`relative ${isCompact ? 'h-32' : 'h-48'} bg-neutral-100 rounded-t-lg overflow-hidden`}>
        {product.images.length > 0 ? (
          <img
            src={product.images[0]}
            alt={product.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <ImageIcon className="w-8 h-8 text-neutral-400" />
          </div>
        )}
        
        {/* Factory Badge */}
        <div className="absolute top-2 left-2">
          <div className="bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
            {product.factory.logo ? (
              <img
                src={product.factory.logo}
                alt={product.factory.name}
                className="w-4 h-4 rounded-full"
              />
            ) : (
              <BuildingIcon className="w-4 h-4 text-neutral-500" />
            )}
            <span className="text-xs font-medium text-neutral-700">
              {product.factory.name}
            </span>
          </div>
        </div>
      </div>
      
      {/* Product Info */}
      <div className={`p-${isCompact ? '3' : '4'} space-y-2`}>
        <div>
          <h3 className={`font-semibold text-neutral-900 ${isCompact ? 'text-sm' : 'text-base'} line-clamp-2`}>
            {product.name}
          </h3>
          <p className="text-xs text-neutral-500 mt-1">
            {product.category.name}
          </p>
        </div>
        
        {!isCompact && product.description && (
          <p className="text-sm text-neutral-600 line-clamp-2">
            {product.description}
          </p>
        )}
        
        <div className="flex items-center justify-between">
          <div>
            <span className={`font-bold text-primary-600 ${isCompact ? 'text-sm' : 'text-lg'}`}>
              {product.currency} {product.basePrice.toLocaleString()}
            </span>
            <span className="text-xs text-neutral-500 ml-1">/ unit</span>
          </div>
        </div>
        
        {/* Actions */}
        <div className={`flex space-x-2 ${isCompact ? 'pt-2' : 'pt-3'}`}>
          <Button
            variant="outline"
            size={isCompact ? 'xs' : 'sm'}
            onClick={() => onView(product.id)}
            className="flex-1"
          >
            View Details
          </Button>
          <Button
            variant="primary"
            size={isCompact ? 'xs' : 'sm'}
            onClick={() => onContact(product.id)}
            className="flex-1"
          >
            Contact
          </Button>
        </div>
      </div>
    </div>
  );
};
```

### **Navigation Components**

#### **Sidebar Navigation**
```tsx
// React Sidebar Navigation
interface SidebarProps {
  user: {
    name: string;
    email: string;
    avatar?: string;
    role: 'FACTORY_ADMIN' | 'FACTORY_STAFF' | 'CUSTOMER';
  };
  factory?: {
    name: string;
    logo?: string;
  };
  currentPath: string;
  onNavigate: (path: string) => void;
  onLogout: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  user,
  factory,
  currentPath,
  onNavigate,
  onLogout
}) => {
  const isFactory = user.role === 'FACTORY_ADMIN' || user.role === 'FACTORY_STAFF';
  
  const factoryNavItems = [
    { path: '/dashboard', label: 'Dashboard', icon: HomeIcon },
    { path: '/products', label: 'Products', icon: CubeIcon },
    { path: '/orders', label: 'Orders', icon: ShoppingBagIcon },
    { path: '/customers', label: 'Customers', icon: UsersIcon },
    { path: '/messages', label: 'Messages', icon: ChatBubbleLeftIcon },
    { path: '/analytics', label: 'Analytics', icon: ChartBarIcon },
    { path: '/settings', label: 'Settings', icon: CogIcon },
  ];
  
  const customerNavItems = [
    { path: '/marketplace', label: 'Marketplace', icon: BuildingStorefrontIcon },
    { path: '/orders', label: 'My Orders', icon: ShoppingBagIcon },
    { path: '/messages', label: 'Messages', icon: ChatBubbleLeftIcon },
    { path: '/quotes', label: 'Quotes', icon: DocumentTextIcon },
    { path: '/account', label: 'Account', icon: UserIcon },
  ];
  
  const navItems = isFactory ? factoryNavItems : customerNavItems;
  
  return (
    <div className="flex flex-col h-full bg-white border-r border-neutral-200">
      {/* Header */}
      <div className="p-4 border-b border-neutral-200">
        {factory && isFactory ? (
          <div className="flex items-center space-x-3">
            {factory.logo ? (
              <img
                src={factory.logo}
                alt={factory.name}
                className="w-8 h-8 rounded-lg"
              />
            ) : (
              <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                <BuildingIcon className="w-5 h-5 text-primary-600" />
              </div>
            )}
            <div>
              <h2 className="font-semibold text-neutral-900 text-sm">
                {factory.name}
              </h2>
              <p className="text-xs text-neutral-500">Factory Portal</p>
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">FC</span>
            </div>
            <div>
              <h2 className="font-semibold text-neutral-900 text-sm">
                FC-CHINA
              </h2>
              <p className="text-xs text-neutral-500">Customer Portal</p>
            </div>
          </div>
        )}
      </div>
      
      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1">
        {navItems.map((item) => {
          const isActive = currentPath === item.path;
          const Icon = item.icon;
          
          return (
            <button
              key={item.path}
              onClick={() => onNavigate(item.path)}
              className={`
                w-full flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors
                ${
                  isActive
                    ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600'
                    : 'text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900'
                }
              `}
            >
              <Icon className="w-5 h-5" />
              <span>{item.label}</span>
            </button>
          );
        })}
      </nav>
      
      {/* User Profile */}
      <div className="p-4 border-t border-neutral-200">
        <div className="flex items-center space-x-3 mb-3">
          {user.avatar ? (
            <img
              src={user.avatar}
              alt={user.name}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <div className="w-8 h-8 bg-neutral-200 rounded-full flex items-center justify-center">
              <UserIcon className="w-5 h-5 text-neutral-500" />
            </div>
          )}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-neutral-900 truncate">
              {user.name}
            </p>
            <p className="text-xs text-neutral-500 truncate">
              {user.email}
            </p>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onLogout}
          className="w-full justify-start"
          icon={<ArrowRightOnRectangleIcon className="w-4 h-4" />}
        >
          Sign Out
        </Button>
      </div>
    </div>
  );
};
```

---

## 📱 **Mobile Design Patterns**

### **Mobile Navigation**

#### **Bottom Tab Navigation (Flutter)**
```dart
// Flutter Bottom Navigation
class FCBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final UserRole userRole;
  
  const FCBottomNavigation({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.userRole,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final items = _getNavigationItems();
    
    return BottomNavigationBar(
      currentIndex: currentIndex,
      onTap: onTap,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: FCColors.primary600,
      unselectedItemColor: FCColors.neutral400,
      backgroundColor: Colors.white,
      elevation: 8,
      items: items.map((item) => BottomNavigationBarItem(
        icon: Icon(item.icon),
        activeIcon: Icon(item.activeIcon ?? item.icon),
        label: item.label,
      )).toList(),
    );
  }
  
  List<NavigationItem> _getNavigationItems() {
    if (userRole == UserRole.customer) {
      return [
        NavigationItem(
          icon: Icons.store_outlined,
          activeIcon: Icons.store,
          label: 'Marketplace',
        ),
        NavigationItem(
          icon: Icons.shopping_bag_outlined,
          activeIcon: Icons.shopping_bag,
          label: 'Orders',
        ),
        NavigationItem(
          icon: Icons.chat_bubble_outline,
          activeIcon: Icons.chat_bubble,
          label: 'Messages',
        ),
        NavigationItem(
          icon: Icons.person_outline,
          activeIcon: Icons.person,
          label: 'Account',
        ),
      ];
    } else {
      return [
        NavigationItem(
          icon: Icons.dashboard_outlined,
          activeIcon: Icons.dashboard,
          label: 'Dashboard',
        ),
        NavigationItem(
          icon: Icons.inventory_2_outlined,
          activeIcon: Icons.inventory_2,
          label: 'Products',
        ),
        NavigationItem(
          icon: Icons.shopping_cart_outlined,
          activeIcon: Icons.shopping_cart,
          label: 'Orders',
        ),
        NavigationItem(
          icon: Icons.chat_outlined,
          activeIcon: Icons.chat,
          label: 'Messages',
        ),
      ];
    }
  }
}
```

### **Mobile Product List**
```dart
// Flutter Product List Item
class ProductListItem extends StatelessWidget {
  final Product product;
  final VoidCallback onTap;
  final VoidCallback onContact;
  
  const ProductListItem({
    Key? key,
    required this.product,
    required this.onTap,
    required this.onContact,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 80,
                  height: 80,
                  color: FCColors.neutral100,
                  child: product.images.isNotEmpty
                      ? CachedNetworkImage(
                          imageUrl: product.images.first,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => const Center(
                            child: CircularProgressIndicator(),
                          ),
                          errorWidget: (context, url, error) => const Icon(
                            Icons.image_not_supported,
                            color: FCColors.neutral400,
                          ),
                        )
                      : const Icon(
                          Icons.image,
                          color: FCColors.neutral400,
                          size: 32,
                        ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Product Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: FCColors.neutral900,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    Text(
                      product.category.name,
                      style: const TextStyle(
                        fontSize: 12,
                        color: FCColors.neutral500,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Factory Info
                    Row(
                      children: [
                        Icon(
                          Icons.business,
                          size: 14,
                          color: FCColors.neutral400,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            product.factory.name,
                            style: const TextStyle(
                              fontSize: 12,
                              color: FCColors.neutral600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Price and Actions
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${product.currency} ${product.basePrice.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: FCColors.primary600,
                          ),
                        ),
                        
                        FCButton(
                          text: 'Contact',
                          onPressed: onContact,
                          variant: FCButtonVariant.primary,
                          size: FCButtonSize.small,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

---

## 🎯 **Responsive Design Guidelines**

### **Breakpoint System**

```css
/* Mobile First Approach */
:root {
  --breakpoint-sm: 640px;   /* Small devices */
  --breakpoint-md: 768px;   /* Medium devices */
  --breakpoint-lg: 1024px;  /* Large devices */
  --breakpoint-xl: 1280px;  /* Extra large devices */
  --breakpoint-2xl: 1536px; /* 2X large devices */
}

/* Usage */
.container {
  width: 100%;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    margin: 0 auto;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding: 0 2rem;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}
```

### **Layout Patterns**

#### **Dashboard Layout**
```tsx
// Responsive Dashboard Layout
const DashboardLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  return (
    <div className="h-screen flex overflow-hidden bg-neutral-50">
      {/* Mobile sidebar overlay */}
      <div className={`fixed inset-0 z-40 lg:hidden ${
        sidebarOpen ? 'block' : 'hidden'
      }`}>
        <div className="fixed inset-0 bg-neutral-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <Sidebar />
        </div>
      </div>
      
      {/* Desktop sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64">
          <Sidebar />
        </div>
      </div>
      
      {/* Main content */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Mobile header */}
        <div className="lg:hidden">
          <div className="flex items-center justify-between bg-white px-4 py-2 border-b border-neutral-200">
            <button
              onClick={() => setSidebarOpen(true)}
              className="p-2 rounded-md text-neutral-400 hover:text-neutral-500 hover:bg-neutral-100"
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            <h1 className="text-lg font-semibold text-neutral-900">Dashboard</h1>
            <div className="w-10" /> {/* Spacer */}
          </div>
        </div>
        
        {/* Page content */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
```

---

## ♿ **Accessibility Guidelines**

### **WCAG 2.1 AA Compliance**

#### **Color Contrast**
```css
/* Ensure minimum contrast ratios */
/* Normal text: 4.5:1 */
/* Large text (18px+ or 14px+ bold): 3:1 */
/* UI components: 3:1 */

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-600: #1e40af;
    --neutral-600: #374151;
    --neutral-900: #111827;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

#### **Focus Management**
```css
/* Visible focus indicators */
.focus-visible {
  outline: 2px solid var(--primary-600);
  outline-offset: 2px;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-600);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}
```

#### **Screen Reader Support**
```tsx
// Accessible component example
const AccessibleButton: React.FC<{
  children: React.ReactNode;
  onClick: () => void;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  disabled?: boolean;
}> = ({ children, onClick, ariaLabel, ariaDescribedBy, disabled = false }) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      className="focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-600"
    >
      {children}
    </button>
  );
};

// Live regions for dynamic content
const LiveRegion: React.FC<{
  message: string;
  priority?: 'polite' | 'assertive';
}> = ({ message, priority = 'polite' }) => {
  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
    >
      {message}
    </div>
  );
};
```

---

## 🌐 **Internationalization (i18n)**

### **Text Direction Support**
```css
/* RTL support */
[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

/* Logical properties (preferred) */
.margin-inline-start-4 {
  margin-inline-start: 1rem;
}

.margin-inline-end-4 {
  margin-inline-end: 1rem;
}
```

### **Font Support**
```css
/* Multi-language font stack */
:root {
  --font-chinese: 'Noto Sans SC', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  --font-arabic: 'Noto Sans Arabic', sans-serif;
  --font-japanese: 'Noto Sans JP', sans-serif;
}

/* Language-specific styles */
:lang(zh) {
  font-family: var(--font-chinese);
}

:lang(ar) {
  font-family: var(--font-arabic);
  direction: rtl;
}

:lang(ja) {
  font-family: var(--font-japanese);
}
```

---

## 📏 **Design Tokens**

### **Token Structure**
```json
{
  "color": {
    "primary": {
      "50": { "value": "#eff6ff" },
      "100": { "value": "#dbeafe" },
      "500": { "value": "#3b82f6" },
      "600": { "value": "#2563eb" },
      "900": { "value": "#1e3a8a" }
    },
    "semantic": {
      "success": { "value": "{color.green.500}" },
      "warning": { "value": "{color.amber.500}" },
      "error": { "value": "{color.red.500}" },
      "info": { "value": "{color.blue.500}" }
    }
  },
  "spacing": {
    "xs": { "value": "4px" },
    "sm": { "value": "8px" },
    "md": { "value": "16px" },
    "lg": { "value": "24px" },
    "xl": { "value": "32px" }
  },
  "typography": {
    "fontSize": {
      "xs": { "value": "12px" },
      "sm": { "value": "14px" },
      "base": { "value": "16px" },
      "lg": { "value": "18px" },
      "xl": { "value": "20px" }
    },
    "fontWeight": {
      "normal": { "value": "400" },
      "medium": { "value": "500" },
      "semibold": { "value": "600" },
      "bold": { "value": "700" }
    }
  }
}
```

---

## 🧪 **Testing Guidelines**

### **Visual Regression Testing**
```typescript
// Storybook stories for component testing
export default {
  title: 'Components/Button',
  component: Button,
  parameters: {
    docs: {
      description: {
        component: 'Primary button component with multiple variants and sizes.'
      }
    }
  }
};

export const AllVariants = () => (
  <div className="space-y-4">
    <div className="space-x-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="destructive">Destructive</Button>
    </div>
    
    <div className="space-x-4">
      <Button size="xs">Extra Small</Button>
      <Button size="sm">Small</Button>
      <Button size="md">Medium</Button>
      <Button size="lg">Large</Button>
      <Button size="xl">Extra Large</Button>
    </div>
    
    <div className="space-x-4">
      <Button loading>Loading</Button>
      <Button disabled>Disabled</Button>
      <Button icon={<PlusIcon className="w-4 h-4" />}>With Icon</Button>
    </div>
  </div>
);

// Accessibility testing
export const AccessibilityTest = () => (
  <div className="space-y-4">
    <Button aria-label="Add new item">+</Button>
    <Button aria-describedby="help-text">Help</Button>
    <div id="help-text" className="text-sm text-neutral-600">
      This button provides additional help information
    </div>
  </div>
);
```

### **Cross-Platform Consistency**
```dart
// Flutter widget tests
void main() {
  group('FCButton Widget Tests', () {
    testWidgets('renders with correct text', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FCButton(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        ),
      );
      
      expect(find.text('Test Button'), findsOneWidget);
    });
    
    testWidgets('handles tap events', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FCButton(
              text: 'Tap Me',
              onPressed: () => tapped = true,
            ),
          ),
        ),
      );
      
      await tester.tap(find.text('Tap Me'));
      expect(tapped, isTrue);
    });
    
    testWidgets('shows loading state', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FCButton(
              text: 'Loading',
              loading: true,
              onPressed: () {},
            ),
          ),
        ),
      );
      
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
  });
}
```

---

**This UI/UX Design System provides comprehensive guidelines for creating a consistent, accessible, and professional user experience across all platforms of the FC-CHINA project. All components are designed to be reusable, scalable, and maintainable.**