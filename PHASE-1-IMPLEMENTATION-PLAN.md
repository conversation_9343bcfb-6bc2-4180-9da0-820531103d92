# FC-CHINA Phase 1 Implementation Plan
## Foundation Development (Weeks 1-3)

**Platform Scope**: This implementation focuses exclusively on:
- ✅ **Web Browser Application**: Responsive design for desktop and mobile browsers
- ✅ **Mobile Applications**: Flutter apps for Android and iOS smartphones/tablets
- ❌ **Desktop Applications**: No native desktop apps (Windows/macOS/Linux)

---

## 🎯 **Phase 1 Objectives**

### **Primary Goals**
- ✅ Establish complete monorepo architecture with Turborepo
- ✅ Implement schema-first development with Prisma
- ✅ Set up Auth0 authentication across web and mobile platforms
- ✅ Create foundational UI components: shadcn/ui (web) + Flutter widgets (mobile)
- ✅ Optimize web application for factory users (desktop-first workflows)
- ✅ Optimize mobile application for customer users (mobile-first workflows)
- ✅ Establish CI/CD pipeline with automated testing
- ✅ Deploy staging environments for web and API

### **Success Criteria**
- All applications run locally with single command (`npm run dev:all`)
- Authentication works end-to-end across web and mobile
- Database schema supports multi-tenant architecture
- Basic CRUD operations functional for core entities
- Automated tests pass with >80% coverage
- Staging deployment successful and accessible

---

## 📅 **Week 1: Foundation & Architecture**

### **Day 1-2: Monorepo Setup & Configuration**

#### **Morning Tasks (Schema-First Development)**
```bash
# 1. Initialize Turborepo structure
npx create-turbo@latest fc-china --package-manager npm
cd fc-china

# 2. Configure workspace structure
mkdir -p apps/{api,web,mobile}
mkdir -p packages/{shared-types,ui,eslint-config,typescript-config}
```

**Deliverables:**
- [ ] **Turborepo Configuration**: Complete `turbo.json` with build pipeline
- [ ] **Package.json Scripts**: Unified scripts for dev, build, test, lint
- [ ] **TypeScript Configuration**: Strict mode enabled across all packages
- [ ] **ESLint & Prettier**: Consistent code formatting rules

#### **Afternoon Tasks (Database Schema)**
```typescript
// 3. Create Prisma schema (schema-first approach)
// File: apps/api/prisma/schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Core multi-tenant models
model Factory {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  email       String   @unique
  // ... complete schema as per technical design
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  factoryId String?
  factory   Factory? @relation(fields: [factoryId], references: [id])
  // ... complete user model
}
```

**Deliverables:**
- [ ] **Complete Prisma Schema**: All models with relationships and indexes
- [ ] **Database Migrations**: Initial migration files generated
- [ ] **Prisma Client**: Generated and configured for type safety
- [ ] **Seed Data**: Development data for testing

### **Day 3-4: API Foundation (tRPC + Express)**

#### **Morning Tasks (tRPC Setup)**
```typescript
// 4. Set up tRPC with Express
// File: apps/api/src/server.ts

import express from 'express';
import cors from 'cors';
import { createExpressMiddleware } from '@trpc/server/adapters/express';
import { appRouter } from './routers';
import { createContext } from './lib/context';

const app = express();

app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true,
}));

app.use('/api/trpc', createExpressMiddleware({
  router: appRouter,
  createContext,
}));

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`🚀 API server running on port ${PORT}`);
});
```

**Deliverables:**
- [ ] **Express Server**: Basic server with CORS and middleware
- [ ] **tRPC Router**: Modular router structure with type safety
- [ ] **Context Creation**: User authentication and database context
- [ ] **Error Handling**: Global error handling with TRPCError

#### **Afternoon Tasks (Authentication Middleware)**
```typescript
// 5. Implement Auth0 middleware
// File: apps/api/src/lib/auth.ts

import jwt from 'jsonwebtoken';
import jwksClient from 'jwks-rsa';
import { TRPCError } from '@trpc/server';

const client = jwksClient({
  jwksUri: `https://${process.env.AUTH0_DOMAIN}/.well-known/jwks.json`
});

export const requireAuth = t.middleware(async ({ ctx, next }) => {
  const token = ctx.req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  
  try {
    const decoded = await verifyToken(token);
    const user = await ctx.db.user.findUnique({
      where: { auth0Id: decoded.sub },
      include: { factory: true }
    });
    
    return next({
      ctx: { ...ctx, user }
    });
  } catch (error) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
});
```

**Deliverables:**
- [ ] **Auth0 Integration**: JWT verification and user context
- [ ] **Protected Procedures**: Middleware for authenticated routes
- [ ] **Factory Access Control**: Multi-tenant security implementation
- [ ] **User Management**: Basic user CRUD operations

### **Day 5: Web Application Foundation (Next.js 15)**

#### **Morning Tasks (Next.js Setup)**
```bash
# 6. Initialize Next.js 15 with App Router
cd apps/web
npx create-next-app@latest . --typescript --tailwind --app --src-dir --import-alias "@/*"

# 7. Install additional dependencies
npm install @trpc/client @trpc/react-query @tanstack/react-query
npm install @auth0/nextjs-auth0 @hookform/resolvers react-hook-form zod
```

**Deliverables:**
- [ ] **Next.js 15 Setup**: App Router with TypeScript and Tailwind (desktop-optimized)
- [ ] **tRPC Client**: React Query integration for API calls
- [ ] **Auth0 Integration**: Authentication provider setup
- [ ] **Factory-Focused Layout**: Desktop-first header, sidebar navigation, multi-column layout

#### **Afternoon Tasks (UI Foundation)**
```typescript
// 8. Set up shadcn/ui components
// File: apps/web/src/components/ui/button.tsx

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "underline-offset-4 hover:underline text-primary",
      },
      size: {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)
```

**Deliverables:**
- [ ] **shadcn/ui Setup**: Desktop-optimized components (Button, Input, Card, DataTable, etc.)
- [ ] **Factory Design System**: Professional styling optimized for desktop workflows
- [ ] **Component Library**: Reusable UI components for complex factory operations
- [ ] **Form Handling**: React Hook Form with Zod validation for detailed data entry

---

## 📅 **Week 2: Core Features & Authentication**

### **Day 6-7: Authentication Implementation**

#### **Morning Tasks (Auth0 Configuration)**
```typescript
// 9. Complete Auth0 setup
// File: apps/web/src/app/api/auth/[auth0]/route.ts

import { handleAuth, handleLogin } from '@auth0/nextjs-auth0';

export const GET = handleAuth({
  login: handleLogin({
    authorizationParams: {
      audience: 'https://api.fc-china.com',
      scope: 'openid profile email offline_access'
    }
  })
});
```

**Deliverables:**
- [ ] **Auth0 Routes**: Login, logout, callback handling
- [ ] **User Session**: Session management and token refresh
- [ ] **Protected Pages**: Route protection with middleware
- [ ] **User Profile**: Basic user profile management

#### **Afternoon Tasks (Factory Setup Flow)**
```typescript
// 10. Factory onboarding flow
// File: apps/web/src/app/onboarding/page.tsx

export default function OnboardingPage() {
  const { user } = useUser();
  const createFactory = api.factories.create.useMutation();
  
  const onSubmit = async (data: FactoryFormData) => {
    try {
      await createFactory.mutateAsync(data);
      router.push('/dashboard');
    } catch (error) {
      // Handle error
    }
  };
  
  return (
    <div className="max-w-2xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Set Up Your Factory</h1>
      <FactorySetupForm onSubmit={onSubmit} />
    </div>
  );
}
```

**Deliverables:**
- [ ] **Factory Registration**: Multi-step factory setup form
- [ ] **User Role Assignment**: Factory admin role assignment
- [ ] **Factory Dashboard**: Basic dashboard with metrics
- [ ] **Navigation**: Role-based navigation menu

### **Day 8-9: Product Management Foundation**

#### **Morning Tasks (Product CRUD API)**
```typescript
// 11. Product management tRPC router
// File: apps/api/src/routers/products.ts

export const productsRouter = router({
  getAll: requireFactoryAccess
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().max(100).default(20),
      search: z.string().optional(),
      categoryId: z.string().optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, search, categoryId } = input;
      const skip = (page - 1) * limit;
      
      const where = {
        factoryId: ctx.user.factoryId!,
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(categoryId && { categoryId })
      };
      
      const [products, total] = await Promise.all([
        ctx.db.product.findMany({
          where,
          skip,
          take: limit,
          include: { category: true, images: true },
          orderBy: { createdAt: 'desc' }
        }),
        ctx.db.product.count({ where })
      ]);
      
      return {
        products,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    }),
    
  create: requireFactoryAccess
    .input(createProductSchema)
    .mutation(async ({ input, ctx }) => {
      return ctx.db.product.create({
        data: {
          ...input,
          factoryId: ctx.user.factoryId!,
          createdBy: ctx.user.id
        },
        include: { category: true, images: true }
      });
    }),
    
  update: requireFactoryAccess
    .input(updateProductSchema)
    .mutation(async ({ input, ctx }) => {
      const { id, ...data } = input;
      
      // Verify product belongs to factory
      const product = await ctx.db.product.findFirst({
        where: { id, factoryId: ctx.user.factoryId! }
      });
      
      if (!product) {
        throw new TRPCError({ code: 'NOT_FOUND' });
      }
      
      return ctx.db.product.update({
        where: { id },
        data: { ...data, updatedBy: ctx.user.id },
        include: { category: true, images: true }
      });
    })
});
```

**Deliverables:**
- [ ] **Product API**: Complete CRUD operations with validation
- [ ] **Multi-tenant Security**: Factory-scoped data access
- [ ] **Pagination**: Efficient data loading with pagination
- [ ] **Search & Filtering**: Product search and category filtering

#### **Afternoon Tasks (Product Management UI)**
```typescript
// 12. Product management interface
// File: apps/web/src/app/products/page.tsx

export default function ProductsPage() {
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  
  const { data, isLoading } = api.products.getAll.useQuery({
    page,
    search,
    limit: 20
  });
  
  const createProduct = api.products.create.useMutation({
    onSuccess: () => {
      utils.products.getAll.invalidate();
    }
  });
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Products</h1>
        <Button onClick={() => setShowCreateModal(true)}>
          Add Product
        </Button>
      </div>
      
      <div className="mb-4">
        <Input
          placeholder="Search products..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="max-w-sm"
        />
      </div>
      
      {isLoading ? (
        <ProductListSkeleton />
      ) : (
        <ProductGrid products={data?.products || []} />
      )}
      
      <Pagination
        currentPage={page}
        totalPages={data?.pagination.pages || 1}
        onPageChange={setPage}
      />
    </div>
  );
}
```

**Deliverables:**
- [ ] **Product List**: Paginated product grid with search
- [ ] **Product Form**: Create/edit product form with validation
- [ ] **Image Upload**: Basic image upload functionality
- [ ] **Product Details**: Detailed product view page

### **Day 10: Mobile App Foundation (Flutter - Android & iOS)**

#### **Morning Tasks (Flutter Mobile Project Setup)**
```bash
# 13. Initialize Flutter mobile project with flavors
cd apps/mobile
flutter create . --org com.fcchina --project-name fc_china_mobile --platforms android,ios

# 14. Configure mobile flavors
# File: android/app/build.gradle
android {
    flavorDimensions "app"
    productFlavors {
        factory {
            dimension "app"
            applicationId "com.fcchina.factory.mobile"
            versionNameSuffix "-factory-mobile"
        }
        customer {
            dimension "app"
            applicationId "com.fcchina.customer.mobile"
            versionNameSuffix "-customer-mobile"
        }
    }
}
```

**Deliverables:**
- [ ] **Flutter Mobile Project**: Multi-flavor setup for factory/customer mobile apps
- [ ] **Riverpod Setup**: Mobile state management configuration
- [ ] **API Service**: Mobile HTTP client with authentication
- [ ] **Navigation**: Go Router with mobile-specific routes and responsive design

#### **Afternoon Tasks (Mobile Authentication & Basic UI)**
```dart
// 15. Auth0 Flutter mobile integration
// File: lib/shared/services/auth_service.dart

class MobileAuthService {
  final Auth0 _auth0 = Auth0(
    domain: const String.fromEnvironment('AUTH0_DOMAIN'),
    clientId: const String.fromEnvironment('AUTH0_CLIENT_ID'),
  );

  Future<UserProfile?> login() async {
    try {
      final credentials = await _auth0.webAuthentication(
        scheme: AppConfig.appId, // Mobile app scheme
      ).login(
        audience: 'https://api.fc-china.com',
        scopes: {'openid', 'profile', 'email', 'offline_access'},
      );

      await _storeTokens(credentials);
      return credentials.user;
    } catch (e) {
      throw AuthException('Mobile login failed: ${e.toString()}');
    }
  }
}
```

**Deliverables:**
- [ ] **Auth0 Mobile Integration**: Login/logout functionality for mobile apps
- [ ] **Token Management**: Secure mobile token storage and refresh
- [ ] **Basic Mobile Screens**: Login, dashboard, product list screens optimized for mobile
- [ ] **Mobile Flavor Configuration**: Different themes and navigation per mobile flavor

---

## 📅 **Week 3: Testing, CI/CD & Deployment**

### **Day 11-12: Testing Implementation**

#### **Morning Tasks (Unit & Integration Tests)**
```typescript
// 16. API testing setup
// File: apps/api/src/__tests__/products.test.ts

describe('Products API', () => {
  beforeEach(async () => {
    await resetTestDatabase();
  });
  
  it('creates product with valid data', async () => {
    const ctx = await createTestContext({
      user: { factoryId: 'test-factory', role: 'FACTORY_ADMIN' }
    });
    
    const input = {
      name: 'Test Product',
      description: 'Test Description',
      basePrice: 99.99,
      categoryId: 'test-category'
    };
    
    const result = await appRouter
      .createCaller(ctx)
      .products.create(input);
    
    expect(result).toMatchObject({
      name: 'Test Product',
      basePrice: 99.99,
      factoryId: 'test-factory'
    });
  });
  
  it('enforces factory access control', async () => {
    const ctx = await createTestContext({
      user: { factoryId: 'factory-1', role: 'FACTORY_ADMIN' }
    });
    
    await expect(
      appRouter.createCaller(ctx).products.update({
        id: 'product-from-factory-2',
        name: 'Updated Name'
      })
    ).rejects.toThrow('NOT_FOUND');
  });
});
```

**Deliverables:**
- [ ] **API Tests**: Unit tests for all tRPC procedures
- [ ] **Integration Tests**: Database integration testing
- [ ] **Web Tests**: React component and page testing
- [ ] **Mobile Tests**: Flutter widget and integration tests

#### **Afternoon Tasks (E2E Testing)**
```typescript
// 17. Playwright E2E tests
// File: e2e/factory-workflow.spec.ts

test.describe('Factory Workflow', () => {
  test('complete factory setup and product creation', async ({ page }) => {
    // Login as new user
    await page.goto('/login');
    await page.click('[data-testid="login-button"]');
    
    // Complete factory setup
    await page.waitForURL('/onboarding');
    await page.fill('[data-testid="factory-name"]', 'Test Factory');
    await page.fill('[data-testid="factory-email"]', '<EMAIL>');
    await page.selectOption('[data-testid="industry"]', 'electronics');
    await page.click('[data-testid="submit-factory"]');
    
    // Navigate to products
    await page.waitForURL('/dashboard');
    await page.click('[data-testid="products-nav"]');
    
    // Create product
    await page.click('[data-testid="add-product"]');
    await page.fill('[data-testid="product-name"]', 'E2E Test Product');
    await page.fill('[data-testid="product-price"]', '99.99');
    await page.click('[data-testid="save-product"]');
    
    // Verify product created
    await expect(page.locator('[data-testid="product-list"]'))
      .toContainText('E2E Test Product');
  });
});
```

**Deliverables:**
- [ ] **E2E Test Suite**: Critical user journey testing
- [ ] **Test Data Management**: Seeding and cleanup utilities
- [ ] **CI Integration**: Automated test execution
- [ ] **Coverage Reports**: Test coverage tracking and reporting

### **Day 13-14: CI/CD Pipeline**

#### **Morning Tasks (GitHub Actions)**
```yaml
# 18. CI/CD workflow
# File: .github/workflows/ci-cd.yml

name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run type checking
        run: npm run type-check
      
      - name: Run linting
        run: npm run lint
      
      - name: Run unit tests
        run: npm run test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test
      
      - name: Run E2E tests
        run: npm run test:e2e
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test
  
  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
```

**Deliverables:**
- [ ] **GitHub Actions**: Complete CI/CD pipeline
- [ ] **Automated Testing**: All tests run on PR and push
- [ ] **Type Safety**: TypeScript errors block deployment
- [ ] **Deployment Automation**: Staging deployment on develop branch

#### **Afternoon Tasks (Production Deployment)**
```bash
# 19. Production deployment setup
# Vercel configuration
vercel --prod

# Railway API deployment
railway login
railway link
railway up
```

**Deliverables:**
- [ ] **Staging Environment**: Fully functional staging deployment
- [ ] **Production Setup**: Production environment configuration
- [ ] **Environment Variables**: Secure secret management
- [ ] **Health Checks**: Application health monitoring

### **Day 15: Documentation & Handoff**

#### **Morning Tasks (Documentation)**
```markdown
# 20. Complete documentation
# File: PHASE-1-COMPLETION-REPORT.md

## Phase 1 Completion Report

### ✅ Completed Features
- [x] Monorepo architecture with Turborepo
- [x] Database schema with multi-tenant support
- [x] Auth0 authentication across platforms
- [x] Basic product management (CRUD)
- [x] Responsive web interface
- [x] Mobile app foundation
- [x] CI/CD pipeline with automated testing
- [x] Staging deployment

### 📊 Metrics
- Test Coverage: 85%
- TypeScript Errors: 0
- Performance Score: 95+
- Accessibility Score: 90+

### 🚀 Ready for Phase 2
- User management system
- Advanced product features
- Real-time messaging foundation
- Enhanced mobile functionality
```

**Deliverables:**
- [ ] **API Documentation**: Complete API endpoint documentation
- [ ] **Component Library**: Storybook with all UI components
- [ ] **Development Guide**: Updated setup and contribution guide
- [ ] **Architecture Documentation**: System architecture overview

#### **Afternoon Tasks (Phase 2 Preparation)**
- [ ] **Phase 2 Planning**: Detailed breakdown of next phase
- [ ] **Team Handoff**: Knowledge transfer and code walkthrough
- [ ] **Performance Baseline**: Establish performance benchmarks
- [ ] **Security Audit**: Initial security review and recommendations

---

## 🎯 **Phase 1 Success Metrics**

### **Technical Metrics**
- [ ] **Zero TypeScript Errors**: All code passes strict type checking
- [ ] **Test Coverage**: >80% coverage across all packages
- [ ] **Performance**: Web Vitals scores >90
- [ ] **Accessibility**: WCAG 2.1 AA compliance >90%
- [ ] **Build Time**: Full build completes in <5 minutes

### **Functional Metrics**
- [ ] **Authentication**: 100% success rate for login/logout
- [ ] **Product CRUD**: All operations work correctly
- [ ] **Multi-tenancy**: Factory isolation verified
- [ ] **Mobile Apps**: Both flavors build and run successfully
- [ ] **Deployment**: Staging environment fully operational

### **Quality Metrics**
- [ ] **Code Quality**: ESLint and Prettier rules enforced
- [ ] **Documentation**: All major components documented
- [ ] **Error Handling**: Graceful error handling throughout
- [ ] **Security**: Basic security measures implemented
- [ ] **Performance**: No performance regressions

---

## 🚀 **Phase 2 Preparation**

### **Immediate Next Steps**
1. **User Management**: Complete user roles and permissions
2. **Advanced Product Features**: Categories, variants, bulk operations
3. **Real-time Messaging**: Socket.io implementation
4. **File Upload**: Complete image and document handling
5. **Mobile Enhancement**: Offline sync and push notifications

### **Technical Debt Items**
- [ ] Optimize database queries with proper indexing
- [ ] Implement comprehensive error logging
- [ ] Add performance monitoring and alerting
- [ ] Enhance security with rate limiting
- [ ] Improve mobile app performance

**🎉 Phase 1 Complete**: Foundation is solid and ready for advanced feature development!
