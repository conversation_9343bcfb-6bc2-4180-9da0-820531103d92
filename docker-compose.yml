version: '3.8'

# FC-CHINA Development Environment
# Platform Scope: Web Browser + Mobile (Android & iOS)
# No desktop applications included

services:
  # Note: Using Supabase for PostgreSQL database
  # Local PostgreSQL removed - using https://ejrxrhojmrjpjodogtxq.supabase.co

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: fc-china-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - fc-china-network

  # API Backend
  api:
    build:
      context: ./apps/api
      dockerfile: Dockerfile.dev
    container_name: fc-china-api
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=https://ejrxrhojmrjpjodogtxq.supabase.co
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqcnhyaG9qbXJqcGpvZG9ndHhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDcwNTcsImV4cCI6MjA2ODU4MzA1N30.mqyGNK_0N0oawTGAyNQxAoH7nW8jrLRoSKmOTmySXAc
      - REDIS_URL=redis://redis:6379
      - AUTH0_DOMAIN=${AUTH0_DOMAIN}
      - AUTH0_AUDIENCE=${AUTH0_AUDIENCE}
    ports:
      - "3001:3001"
    volumes:
      - ./apps/api:/app
      - /app/node_modules
    depends_on:
      - redis
    networks:
      - fc-china-network
    command: npm run dev

  # Web Frontend
  web:
    build:
      context: ./apps/web
      dockerfile: Dockerfile.dev
    container_name: fc-china-web
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3001
      - NEXT_PUBLIC_SUPABASE_URL=https://ejrxrhojmrjpjodogtxq.supabase.co
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqcnhyaG9qbXJqcGpvZG9ndHhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDcwNTcsImV4cCI6MjA2ODU4MzA1N30.mqyGNK_0N0oawTGAyNQxAoH7nW8jrLRoSKmOTmySXAc
      - NEXT_PUBLIC_AUTH0_DOMAIN=${AUTH0_DOMAIN}
      - NEXT_PUBLIC_AUTH0_CLIENT_ID=${AUTH0_CLIENT_ID}
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
    ports:
      - "3000:3000"
    volumes:
      - ./apps/web:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - api
    networks:
      - fc-china-network
    command: npm run dev

  # Prisma Studio (Database GUI) - Connected to Supabase
  prisma-studio:
    build:
      context: ./apps/api
      dockerfile: Dockerfile.dev
    container_name: fc-china-prisma-studio
    restart: unless-stopped
    environment:
      - DATABASE_URL=${DATABASE_URL}
    ports:
      - "5555:5555"
    volumes:
      - ./apps/api:/app
      - /app/node_modules
    networks:
      - fc-china-network
    command: npx prisma studio --port 5555 --hostname 0.0.0.0

  # Mailhog (Email Testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: fc-china-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - fc-china-network

  # MinIO (S3-compatible storage for local development)
  minio:
    image: minio/minio:latest
    container_name: fc-china-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - fc-china-network
    command: server /data --console-address ":9001"

volumes:
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  fc-china-network:
    driver: bridge
