# FC-CHINA Development Rules & Standards

## 🎯 **CORE PRINCIPLES**

### **1. Schema-First Development**
- **ALWAYS** start with Prisma schema updates before any code changes
- Database schema is the single source of truth
- Generate Prisma client after every schema modification
- Run type checking before implementing any router or API endpoint
- Document all schema changes with migration notes

### **2. Type Safety Above All**
- **ZERO** TypeScript errors allowed in any commit
- Use generated Prisma types exclusively - never create custom types that duplicate schema
- Implement strict TypeScript configuration across all packages
- Use Zod for runtime validation that matches Prisma models exactly
- Handle Decimal types properly with explicit conversions

### **3. Incremental Development**
- Implement one feature/router at a time
- Validate types and fix errors before moving to next feature
- Never accumulate technical debt
- Test each component thoroughly before integration

---

## 🏗️ **PROJECT ARCHITECTURE RULES**

### **Monorepo Structure (Turborepo)**
```
fc-china/
├── apps/
│   ├── api/                 # tRPC + Express + Prisma backend
│   ├── web/                 # Next.js 15 + TypeScript frontend
│   └── mobile/              # Single Flutter app with flavors
├── packages/
│   ├── shared-types/        # Shared TypeScript types
│   ├── ui/                  # Shared React components (shadcn/ui)
│   ├── eslint-config/       # ESLint configuration
│   └── typescript-config/   # TypeScript configurations
└── docs/                    # Documentation
```

### **Technology Stack Compliance**
- **Backend**: Node.js + Express + TypeScript + tRPC + Prisma + Supabase
- **Frontend**: Next.js 15 + TypeScript + Tailwind CSS + TanStack Query + shadcn/ui
- **Mobile**: Flutter + Riverpod + Flavors (factory/customer)
- **Database**: Supabase (PostgreSQL) with Row Level Security
- **Authentication**: Auth0 across all platforms
- **Real-time**: Socket.io + Supabase Realtime
- **Validation**: Zod (must match Prisma schemas)

---

## 📋 **DEVELOPMENT WORKFLOW RULES**

### **Feature Development Cycle (MANDATORY)**
1. **Schema Update** (if needed)
   - Update Prisma schema first
   - Run `prisma generate`
   - Run `prisma db push` or create migration
   - Document changes in schema comments

2. **Type Validation**
   - Run `npm run type-check` across all packages
   - Fix any existing TypeScript errors before proceeding
   - Ensure zero errors before starting new development

3. **Implementation**
   - Implement single router/feature
   - Use only generated Prisma types
   - Follow tRPC procedure patterns
   - Implement proper error handling with TRPCError

4. **Validation & Testing**
   - Run type checking again
   - Test API endpoints with tRPC panel
   - Write unit tests for new procedures
   - Validate multi-tenant isolation

5. **Code Review & Merge**
   - Zero TypeScript errors required
   - All tests passing
   - Schema-code alignment verified
   - Performance benchmarks met

### **Git Workflow**
- Use conventional commits: `feat:`, `fix:`, `docs:`, `refactor:`
- Create feature branches from `main`
- Require PR reviews focusing on type safety
- Run pre-commit hooks for type checking and linting
- Never merge with TypeScript errors

---

## 🔧 **BACKEND DEVELOPMENT RULES**

### **Prisma Schema Guidelines**
- Use descriptive field names that match business logic
- Include proper relations with cascade deletes
- Add indexes for frequently queried fields
- Use enums for status fields
- Include `createdAt` and `updatedAt` for all models
- Implement factory isolation with `factoryId` foreign keys

### **tRPC Procedure Standards**
```typescript
// ✅ CORRECT: Type-safe procedure with proper validation
export const createProduct = protectedProcedure
  .input(z.object({
    name: z.string().min(1),
    price: z.number().positive(),
    factoryId: z.string().cuid(),
  }))
  .mutation(async ({ input, ctx }) => {
    // Validate factory access
    checkFactoryAccess(ctx.user.factoryId, input.factoryId);
    
    return ctx.db.product.create({
      data: {
        ...input,
        // Only use fields that exist in schema
      },
    });
  });

// ❌ WRONG: Using non-existent fields
export const badProcedure = protectedProcedure
  .mutation(async ({ ctx }) => {
    return ctx.db.product.create({
      data: {
        createdById: ctx.user.id, // ❌ Field doesn't exist in schema
        updatedBy: ctx.user,      // ❌ Wrong field name
      },
    });
  });
```

### **Error Handling Standards**
- Use TRPCError with appropriate codes
- Implement proper validation with Zod
- Handle Decimal conversions explicitly
- Log errors appropriately without exposing sensitive data

### **Multi-Tenant Security**
- Always validate factory access in procedures
- Use Row Level Security (RLS) policies
- Implement proper user context validation
- Never expose data across factory boundaries

---

## 🌐 **FRONTEND DEVELOPMENT RULES**

### **Next.js 15 Standards**
- Use App Router exclusively
- Implement proper TypeScript throughout
- Use TanStack Query for all tRPC calls
- Follow shadcn/ui component patterns
- Implement proper error boundaries
- Use Tailwind CSS for all styling

### **State Management**
- Use TanStack Query for server state
- Use React state for local UI state
- Implement proper loading and error states
- Cache data appropriately with TanStack Query

### **Authentication Integration**
- Use Auth0 React SDK
- Implement protected routes properly
- Handle token refresh automatically
- Implement role-based component rendering

---

## 📱 **MOBILE DEVELOPMENT RULES**

### **Flutter App Structure**
- Single app with factory and customer flavors
- Shared components in `/lib/shared/`
- Flavor-specific code in `/lib/flavors/factory/` and `/lib/flavors/customer/`
- Use Riverpod for state management
- Implement proper offline capabilities

### **Flavor Configuration**
- Separate entry points: `main_factory.dart` and `main_customer.dart`
- Different app icons, names, and themes per flavor
- Flavor-specific navigation and features
- Shared authentication and API services

### **Performance Standards**
- Implement lazy loading for large lists
- Use proper image caching
- Implement offline-first architecture
- Optimize app size and startup time

---

## 🚫 **CRITICAL ANTI-PATTERNS TO AVOID**

### **Schema-Related Violations**
- ❌ Never reference non-existent schema fields
- ❌ Never use `any` type without explicit justification
- ❌ Never skip Decimal to number conversions
- ❌ Never create custom types that duplicate Prisma types
- ❌ Never commit code with TypeScript errors

### **API Development Violations**
- ❌ Never skip input validation with Zod
- ❌ Never expose internal error details to clients
- ❌ Never skip factory access validation
- ❌ Never use untyped database queries

### **Frontend Violations**
- ❌ Never skip error handling in components
- ❌ Never use inline styles instead of Tailwind
- ❌ Never skip loading states for async operations
- ❌ Never expose sensitive data in client-side code

---

## 🔍 **CODE QUALITY STANDARDS**

### **TypeScript Configuration**
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

### **ESLint Rules**
- Enforce consistent import/export patterns
- Require explicit return types for functions
- Prevent unused variables and imports
- Enforce proper error handling

### **Testing Requirements**
- Unit tests for all tRPC procedures
- Integration tests for critical workflows
- End-to-end tests for user journeys
- Flutter widget tests for shared components
- Minimum 80% code coverage

---

## 🛠️ **DEVELOPMENT TOOLS & SETUP**

### **Required VS Code Extensions**
- Prisma (for schema editing)
- TypeScript Hero (import management)
- ESLint (code quality)
- Prettier (code formatting)
- Flutter (mobile development)
- Tailwind CSS IntelliSense

### **Package Scripts (Standardized)**
```json
{
  "scripts": {
    "dev": "turbo run dev",
    "build": "turbo run build",
    "type-check": "turbo run type-check",
    "lint": "turbo run lint",
    "test": "turbo run test",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev"
  }
}
```

### **Pre-commit Hooks**
- Type checking across all packages
- ESLint validation
- Prettier formatting
- Test execution
- Schema validation

---

## 🚨 **EMERGENCY PROTOCOLS**

### **When Type Errors Accumulate**
1. **STOP** all new feature development immediately
2. Create emergency branch for fixes
3. Fix errors incrementally, one file at a time
4. Update schema if fields are legitimately needed
5. Remove references to non-existent fields
6. Add proper type conversions and error handling
7. Strengthen pre-commit hooks to prevent recurrence

### **Performance Issues**
1. Profile database queries with Prisma
2. Optimize API response times
3. Implement proper caching strategies
4. Review and optimize component rendering

---

## 📊 **SUCCESS METRICS**

### **Technical KPIs**
- Zero TypeScript errors in production
- API response time < 150ms
- 99.9% uptime SLA
- < 2 second page load times
- 95% automated test coverage

### **Development KPIs**
- 100% schema-code alignment
- Zero critical security vulnerabilities
- 90%+ code review approval rate
- < 1 day average PR merge time

---

## 🎯 **PHASE-SPECIFIC RULES**

### **Phase 1: Foundation (Weeks 1-3)**
- Focus on monorepo setup and basic architecture
- Establish all tooling and development workflows
- Create basic Prisma schema with core models
- Set up authentication infrastructure
- **Success Criteria**: All apps run locally with single command

### **Phase 2: Authentication (Weeks 4-5)**
- Implement Auth0 across all platforms
- Create multi-tenant architecture
- Build factory setup wizard
- **Success Criteria**: Authentication works across all platforms

### **Phase 3: Product Management (Weeks 6-8)**
- Build product CRUD operations
- Implement file upload with Supabase Storage
- Create admin and customer product interfaces
- **Success Criteria**: Product management functional on all interfaces

### **Phase 4: Messaging (Weeks 9-10)**
- Implement real-time messaging with Socket.io
- Create chat interfaces for both flavors
- **Success Criteria**: Real-time messaging operational

### **Phase 5: Orders & Quotes (Weeks 11-13)**
- Build quote and order management systems
- Implement workflow automation
- **Success Criteria**: Complete order workflow functional

### **Phase 6: Analytics (Weeks 14-15)**
- Create analytics dashboards
- Implement user management
- **Success Criteria**: Analytics and user management complete

### **Phase 7: Deployment (Weeks 16-18)**
- Optimize performance
- Deploy to production
- Submit mobile app to stores
- **Success Criteria**: Production deployment successful

---

## 📝 **DOCUMENTATION REQUIREMENTS**

### **Code Documentation**
- Document all tRPC procedures with JSDoc
- Include examples for complex operations
- Document schema changes in migration files
- Maintain API documentation with examples

### **Architecture Documentation**
- Keep architecture decisions recorded
- Document multi-tenant security patterns
- Maintain deployment and setup guides
- Create troubleshooting documentation

---

## ✅ **DAILY CHECKLIST**

### **Before Starting Development**
- [ ] Pull latest changes from main branch
- [ ] Run `npm run type-check` to ensure clean state
- [ ] Review any schema changes from team
- [ ] Check for any breaking changes in dependencies

### **Before Committing**
- [ ] Run `npm run type-check` - must pass with zero errors
- [ ] Run `npm run lint` - must pass
- [ ] Run `npm run test` - all tests must pass
- [ ] Verify schema-code alignment
- [ ] Test functionality manually

### **Before Merging PR**
- [ ] All CI checks passing
- [ ] Code review approved
- [ ] No TypeScript errors
- [ ] Performance impact assessed
- [ ] Documentation updated if needed

---

## 🎓 **LEARNING RESOURCES**

### **Required Reading**
- [tRPC Documentation](https://trpc.io/docs)
- [Prisma Best Practices](https://www.prisma.io/docs/guides/performance-and-optimization)
- [Next.js 15 App Router](https://nextjs.org/docs/app)
- [Flutter Flavors Guide](https://docs.flutter.dev/deployment/flavors)
- [Supabase Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)

### **Team Knowledge Sharing**
- Weekly architecture reviews
- Code review sessions focusing on type safety
- Monthly retrospectives on development process
- Quarterly technology stack updates

---

## 🔒 **SECURITY & COMPLIANCE STANDARDS**

### **Data Protection Requirements**
- ✅ All PII must be encrypted at rest using AES-256
- ✅ Implement GDPR compliance with data subject rights
- ✅ CCPA compliance for California users
- ✅ Data retention policies: 7 years for business records, 30 days for logs
- ✅ Privacy by design in all feature development
- ✅ Regular security audits and penetration testing

### **Authentication & Authorization**
- ✅ Multi-factor authentication for admin accounts
- ✅ Role-based access control (RBAC) implementation
- ✅ Session management with secure tokens
- ✅ Password policies: minimum 12 characters, complexity requirements
- ✅ Account lockout after 5 failed attempts
- ✅ Regular access reviews and deprovisioning

### **API Security Standards**
- ✅ Rate limiting: 100 requests/minute per user, 1000/minute per factory
- ✅ Input validation and sanitization for all endpoints
- ✅ SQL injection prevention through Prisma ORM
- ✅ XSS protection with Content Security Policy
- ✅ CORS configuration for allowed origins only
- ✅ API versioning and deprecation policies

### **File Upload Security**
- ✅ File type validation (whitelist approach)
- ✅ File size limits: 10MB for images, 50MB for documents
- ✅ Virus scanning for all uploads
- ✅ Secure file storage with access controls
- ✅ Content-Type validation and sanitization

---

## 🏢 **BUSINESS COMPLIANCE STANDARDS**

### **Export/Import Regulations**
- ✅ Compliance with Chinese export regulations
- ✅ International trade compliance (ITAR, EAR)
- ✅ Customs documentation automation
- ✅ Restricted party screening
- ✅ Country-specific trade restrictions

### **Financial Compliance**
- ✅ PCI DSS compliance for payment processing
- ✅ Anti-Money Laundering (AML) procedures
- ✅ Know Your Customer (KYC) verification
- ✅ Financial transaction monitoring
- ✅ Tax compliance automation
- ✅ Invoice and receipt management

### **Industry Certifications**
- ✅ ISO 9001 quality management integration
- ✅ ISO 27001 information security compliance
- ✅ Industry-specific certifications tracking
- ✅ Supplier certification management
- ✅ Product compliance documentation

---

## 📊 **PERFORMANCE & MONITORING STANDARDS**

### **Performance SLAs**
- ✅ Page load time: < 2 seconds (95th percentile)
- ✅ API response time: < 150ms (average)
- ✅ Search response time: < 500ms
- ✅ Database query time: < 100ms (95th percentile)
- ✅ System uptime: 99.9% availability
- ✅ Error rate: < 0.1% of all requests

### **Scalability Requirements**
- ✅ Support 10,000+ concurrent users per factory
- ✅ Handle 1M+ products in catalog
- ✅ Process 100,000+ daily transactions
- ✅ Support 50+ geographic regions
- ✅ Auto-scaling based on load
- ✅ CDN implementation for global performance

### **Monitoring & Alerting**
- ✅ Application Performance Monitoring (APM)
- ✅ Real-time error tracking with Sentry
- ✅ Infrastructure monitoring
- ✅ Business metrics dashboards
- ✅ Automated alerting for SLA breaches
- ✅ 24/7 monitoring coverage

---

## 🔄 **OPERATIONAL PROCEDURES**

### **Deployment Standards**
- ✅ Blue-green deployment strategy
- ✅ Automated rollback procedures
- ✅ Database migration safety checks
- ✅ Feature flag implementation
- ✅ Canary releases for critical features
- ✅ Post-deployment verification

### **Backup & Disaster Recovery**
- ✅ Daily automated database backups
- ✅ Cross-region backup replication
- ✅ 4-hour Recovery Time Objective (RTO)
- ✅ 1-hour Recovery Point Objective (RPO)
- ✅ Quarterly disaster recovery testing
- ✅ Business continuity planning

### **Incident Management**
- ✅ 24/7 incident response team
- ✅ Severity classification system
- ✅ Escalation procedures
- ✅ Post-incident reviews
- ✅ Root cause analysis
- ✅ Communication protocols

---

## 🌐 **INTERNATIONALIZATION STANDARDS**

### **Language Support**
- ✅ English (primary)
- ✅ Simplified Chinese
- ✅ Traditional Chinese
- ✅ Spanish
- ✅ French
- ✅ German
- ✅ Japanese
- ✅ Korean

### **Localization Requirements**
- ✅ Currency conversion and display
- ✅ Date/time format localization
- ✅ Number format localization
- ✅ Address format validation
- ✅ Cultural adaptation of UI/UX
- ✅ Local payment method integration
- ✅ Timezone handling

---

## 📱 **MOBILE-SPECIFIC STANDARDS**

### **Performance Requirements**
- ✅ App startup time: < 3 seconds
- ✅ Screen transition time: < 300ms
- ✅ Offline functionality for core features
- ✅ Battery optimization
- ✅ Memory usage optimization
- ✅ Network efficiency

### **Platform Compliance**
- ✅ iOS App Store guidelines compliance
- ✅ Google Play Store policies compliance
- ✅ Platform-specific design guidelines
- ✅ Accessibility standards (WCAG 2.1 AA)
- ✅ Privacy policy integration
- ✅ Terms of service acceptance

---

## 🧪 **TESTING STANDARDS**

### **Test Coverage Requirements**
- ✅ Unit tests: 90% code coverage
- ✅ Integration tests: All API endpoints
- ✅ End-to-end tests: Critical user journeys
- ✅ Performance tests: Load and stress testing
- ✅ Security tests: Vulnerability scanning
- ✅ Accessibility tests: WCAG compliance

### **Testing Automation**
- ✅ Automated test execution in CI/CD
- ✅ Cross-browser testing
- ✅ Mobile device testing
- ✅ API contract testing
- ✅ Visual regression testing
- ✅ Database migration testing

---

## 📈 **ANALYTICS & REPORTING STANDARDS**

### **Business Intelligence**
- ✅ Real-time analytics dashboards
- ✅ Custom report generation
- ✅ Data export capabilities
- ✅ Predictive analytics
- ✅ Performance benchmarking
- ✅ ROI tracking and analysis

### **Data Governance**
- ✅ Data quality monitoring
- ✅ Data lineage tracking
- ✅ Master data management
- ✅ Data catalog maintenance
- ✅ Metadata management
- ✅ Data access controls

---

## 🔧 **MAINTENANCE & SUPPORT STANDARDS**

### **Technical Debt Management**
- ✅ Monthly technical debt assessment
- ✅ Refactoring sprint allocation (20% of capacity)
- ✅ Code quality metrics tracking
- ✅ Dependency update schedule
- ✅ Performance optimization cycles
- ✅ Security patch management

### **Documentation Maintenance**
- ✅ API documentation auto-generation
- ✅ User manual updates with releases
- ✅ Technical documentation reviews
- ✅ Knowledge base maintenance
- ✅ Training material updates
- ✅ Troubleshooting guide updates

---

**Remember: These rules are not suggestions - they are mandatory standards for the FC-CHINA project. Following them ensures type safety, maintainability, security, compliance, and successful project delivery within the 16-18 week timeline.**