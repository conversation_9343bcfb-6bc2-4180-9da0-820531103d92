{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@fc-china/shared-types": ["./packages/shared-types/src"], "@fc-china/ui": ["./packages/ui/src"], "@fc-china/eslint-config": ["./packages/eslint-config"], "@fc-china/typescript-config": ["./packages/typescript-config"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "apps/mobile/**/*"]}