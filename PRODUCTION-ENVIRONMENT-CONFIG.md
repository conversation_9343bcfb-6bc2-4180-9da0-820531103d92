# Production Environment Configuration

## 🏭 **Enterprise Environment Management**

### **Environment-Specific Configuration**

#### **Development Environment**
```bash
# .env.development
NODE_ENV=development
LOG_LEVEL=debug
DATABASE_URL="postgresql://fc_china_user:${DB_PASSWORD}@localhost:5432/fc_china_dev?sslmode=prefer&connect_timeout=10&pool_timeout=20&pool_max=10"
REDIS_URL="redis://localhost:6379/0"
CORS_ORIGIN="http://localhost:3000,http://localhost:3001"
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
ENABLE_QUERY_LOGGING=true
ENABLE_DEBUG_ROUTES=true
```

#### **Staging Environment**
```bash
# .env.staging
NODE_ENV=staging
LOG_LEVEL=info
DATABASE_URL="postgresql://fc_china_user:${DB_PASSWORD}@${DB_HOST}:5432/fc_china_staging?sslmode=require&connect_timeout=10&pool_timeout=30&pool_max=20&pool_min=5"
REDIS_URL="rediss://:${REDIS_PASSWORD}@${REDIS_HOST}:6380/0?tls_check_hostname=false"
CORS_ORIGIN="https://staging.fc-china.com,https://api-staging.fc-china.com"
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=500
ENABLE_QUERY_LOGGING=false
ENABLE_DEBUG_ROUTES=false
SESSION_SECRET="${SESSION_SECRET_32_CHARS}"
ENCRYPTION_KEY="${ENCRYPTION_KEY_32_CHARS}"
```

#### **Production Environment**
```bash
# .env.production
NODE_ENV=production
LOG_LEVEL=warn
DATABASE_URL="postgresql://fc_china_user:${DB_PASSWORD}@${DB_HOST}:5432/fc_china_prod?sslmode=require&connect_timeout=10&pool_timeout=30&pool_max=50&pool_min=10&statement_timeout=30000"
REDIS_URL="rediss://:${REDIS_PASSWORD}@${REDIS_HOST}:6380/0?tls_check_hostname=true"
CORS_ORIGIN="https://fc-china.com,https://api.fc-china.com"
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
ENABLE_QUERY_LOGGING=false
ENABLE_DEBUG_ROUTES=false
SESSION_SECRET="${SESSION_SECRET_32_CHARS}"
ENCRYPTION_KEY="${ENCRYPTION_KEY_32_CHARS}"

# Production Security
FORCE_HTTPS=true
HSTS_MAX_AGE=31536000
CSP_REPORT_URI="https://api.fc-china.com/security/csp-report"
SECURITY_HEADERS_ENABLED=true

# Production Monitoring
SENTRY_DSN="${SENTRY_DSN}"
DATADOG_API_KEY="${DATADOG_API_KEY}"
HEALTH_CHECK_TOKEN="${HEALTH_CHECK_TOKEN}"

# Production Integrations
AUTH0_DOMAIN="${AUTH0_PROD_DOMAIN}"
AUTH0_CLIENT_ID="${AUTH0_PROD_CLIENT_ID}"
AUTH0_CLIENT_SECRET="${AUTH0_PROD_CLIENT_SECRET}"
SUPABASE_URL="https://ejrxrhojmrjpjodogtxq.supabase.co"
SUPABASE_SERVICE_KEY="${SUPABASE_PROD_SERVICE_KEY}"
```

### **Configuration Validation**
```typescript
// src/lib/config.ts
import { z } from 'zod';

const configSchema = z.object({
  NODE_ENV: z.enum(['development', 'staging', 'production']),
  PORT: z.coerce.number().default(3001),
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url(),
  
  // Authentication
  AUTH0_DOMAIN: z.string().min(1),
  AUTH0_CLIENT_ID: z.string().min(1),
  AUTH0_CLIENT_SECRET: z.string().min(1),
  AUTH0_AUDIENCE: z.string().url(),
  
  // Security
  SESSION_SECRET: z.string().min(32),
  ENCRYPTION_KEY: z.string().min(32),
  JWT_SECRET: z.string().min(32),
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.coerce.number().default(60000),
  RATE_LIMIT_MAX_REQUESTS: z.coerce.number().default(100),
  
  // Feature Flags
  ENABLE_QUERY_LOGGING: z.coerce.boolean().default(false),
  ENABLE_DEBUG_ROUTES: z.coerce.boolean().default(false),
  FORCE_HTTPS: z.coerce.boolean().default(false),
  
  // External Services
  SUPABASE_URL: z.string().url(),
  SUPABASE_SERVICE_KEY: z.string().min(1),
  SENTRY_DSN: z.string().url().optional(),
  DATADOG_API_KEY: z.string().optional(),
});

export type Config = z.infer<typeof configSchema>;

export const config: Config = configSchema.parse(process.env);

// Environment-specific validations
if (config.NODE_ENV === 'production') {
  if (!config.SENTRY_DSN) {
    throw new Error('SENTRY_DSN is required in production');
  }
  if (!config.DATADOG_API_KEY) {
    throw new Error('DATADOG_API_KEY is required in production');
  }
  if (config.ENABLE_DEBUG_ROUTES) {
    throw new Error('Debug routes must be disabled in production');
  }
}
```

### **Database Connection Management**
```typescript
// src/lib/database.ts
import { PrismaClient } from '@prisma/client';
import { config } from './config';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: config.ENABLE_QUERY_LOGGING 
    ? ['query', 'info', 'warn', 'error']
    : ['warn', 'error'],
  
  datasources: {
    db: {
      url: config.DATABASE_URL,
    },
  },
  
  // Production optimizations
  ...(config.NODE_ENV === 'production' && {
    errorFormat: 'minimal',
    log: ['error'],
  }),
});

if (config.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// Connection health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});
```

### **Redis Connection Management**
```typescript
// src/lib/redis.ts
import Redis from 'ioredis';
import { config } from './config';

const redisConfig = {
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxLoadingTimeout: 5000,
  lazyConnect: true,
  
  // Production optimizations
  ...(config.NODE_ENV === 'production' && {
    connectTimeout: 10000,
    commandTimeout: 5000,
    family: 4,
    keepAlive: 30000,
  }),
};

export const redis = new Redis(config.REDIS_URL, redisConfig);

redis.on('error', (error) => {
  console.error('Redis connection error:', error);
});

redis.on('connect', () => {
  console.log('Redis connected successfully');
});

redis.on('ready', () => {
  console.log('Redis ready for commands');
});

// Connection health check
export async function checkRedisHealth(): Promise<boolean> {
  try {
    const result = await redis.ping();
    return result === 'PONG';
  } catch (error) {
    console.error('Redis health check failed:', error);
    return false;
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  await redis.quit();
});

process.on('SIGTERM', async () => {
  await redis.quit();
});
```

### **Secrets Management**
```typescript
// src/lib/secrets.ts
import { createCipheriv, createDecipheriv, randomBytes } from 'crypto';
import { config } from './config';

const ALGORITHM = 'aes-256-gcm';
const KEY = Buffer.from(config.ENCRYPTION_KEY, 'hex');

export class SecretsManager {
  static encrypt(text: string): string {
    const iv = randomBytes(16);
    const cipher = createCipheriv(ALGORITHM, KEY, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }
  
  static decrypt(encryptedData: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedData.split(':');
    
    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');
    
    const decipher = createDecipheriv(ALGORITHM, KEY, iv);
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
  
  static generateSecureToken(length: number = 32): string {
    return randomBytes(length).toString('hex');
  }
  
  static hashPassword(password: string): Promise<string> {
    // Use bcrypt or argon2 for password hashing
    // Implementation depends on chosen library
    throw new Error('Password hashing not implemented - use bcrypt or argon2');
  }
}
```

### **Environment Promotion Strategy**
```bash
#!/bin/bash
# scripts/promote-environment.sh

set -e

SOURCE_ENV=$1
TARGET_ENV=$2

if [[ -z "$SOURCE_ENV" || -z "$TARGET_ENV" ]]; then
  echo "Usage: $0 <source-env> <target-env>"
  echo "Example: $0 staging production"
  exit 1
fi

echo "🚀 Promoting from $SOURCE_ENV to $TARGET_ENV"

# 1. Validate source environment
echo "📋 Validating source environment..."
npm run validate:env --env=$SOURCE_ENV

# 2. Run tests
echo "🧪 Running test suite..."
npm run test:all

# 3. Build application
echo "🔨 Building application..."
npm run build

# 4. Database migration check
echo "🗄️ Checking database migrations..."
npm run db:migrate:status --env=$TARGET_ENV

# 5. Deploy to target environment
echo "🚀 Deploying to $TARGET_ENV..."
case $TARGET_ENV in
  "staging")
    npm run deploy:staging
    ;;
  "production")
    npm run deploy:production
    ;;
  *)
    echo "Unknown target environment: $TARGET_ENV"
    exit 1
    ;;
esac

# 6. Health check
echo "🏥 Running health checks..."
npm run health-check --env=$TARGET_ENV

echo "✅ Successfully promoted to $TARGET_ENV"
```
