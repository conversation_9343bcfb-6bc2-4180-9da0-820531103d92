# Production Monitoring & Logging

## 📊 **Enterprise Monitoring & Observability**

### **Structured Logging Implementation**

#### **Production Logger Configuration**
```typescript
// src/lib/logging/logger.ts
import winston from 'winston';
import { config } from '../config';

// Custom log format for production
const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return JSON.stringify({
      timestamp,
      level,
      message,
      service: 'fc-china-api',
      environment: config.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      ...meta,
    });
  })
);

// Development format for readability
const developmentFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  })
);

// Create logger instance
export const logger = winston.createLogger({
  level: config.LOG_LEVEL || 'info',
  format: config.NODE_ENV === 'production' ? productionFormat : developmentFormat,
  defaultMeta: {
    service: 'fc-china-api',
    environment: config.NODE_ENV,
  },
  transports: [
    // Console transport
    new winston.transports.Console({
      handleExceptions: true,
      handleRejections: true,
    }),
    
    // File transports for production
    ...(config.NODE_ENV === 'production' ? [
      new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 5,
        tailable: true,
      }),
      new winston.transports.File({
        filename: 'logs/combined.log',
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 10,
        tailable: true,
      }),
    ] : []),
  ],
  
  // Exit on error
  exitOnError: false,
});

// Performance logger for slow queries and operations
export const performanceLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: 'logs/performance.log',
      maxsize: 10 * 1024 * 1024,
      maxFiles: 5,
    }),
  ],
});

// Security logger for audit events
export const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: 'logs/security.log',
      maxsize: 10 * 1024 * 1024,
      maxFiles: 10,
    }),
    // Also send to console in development
    ...(config.NODE_ENV !== 'production' ? [
      new winston.transports.Console()
    ] : []),
  ],
});
```

#### **Request Logging Middleware**
```typescript
// src/lib/logging/middleware.ts
import { Request, Response, NextFunction } from 'express';
import { logger, performanceLogger } from './logger';
import { v4 as uuidv4 } from 'uuid';

export function requestLogger(req: Request, res: Response, next: NextFunction) {
  const requestId = uuidv4();
  const startTime = Date.now();
  
  // Add request ID to request object
  (req as any).requestId = requestId;
  
  // Log incoming request
  logger.info('Incoming request', {
    requestId,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    contentLength: req.headers['content-length'],
    timestamp: new Date().toISOString(),
  });
  
  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const duration = Date.now() - startTime;
    
    // Log response
    logger.info('Request completed', {
      requestId,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      contentLength: res.get('content-length'),
      timestamp: new Date().toISOString(),
    });
    
    // Log slow requests
    if (duration > 1000) {
      performanceLogger.warn('Slow request detected', {
        requestId,
        method: req.method,
        url: req.url,
        duration,
        statusCode: res.statusCode,
        timestamp: new Date().toISOString(),
      });
    }
    
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
}

export function errorLogger(error: Error, req: Request, res: Response, next: NextFunction) {
  const requestId = (req as any).requestId;
  
  logger.error('Request error', {
    requestId,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    timestamp: new Date().toISOString(),
  });
  
  // Don't expose internal errors in production
  if (res.headersSent) {
    return next(error);
  }
  
  const statusCode = (error as any).statusCode || 500;
  const message = config.NODE_ENV === 'production' && statusCode === 500
    ? 'Internal Server Error'
    : error.message;
  
  res.status(statusCode).json({
    error: {
      message,
      requestId,
      timestamp: new Date().toISOString(),
    },
  });
}
```

### **Audit Logging System**

#### **Comprehensive Audit Logger**
```typescript
// src/lib/logging/audit.ts
import { prisma } from '../database/connection';
import { securityLogger, logger } from './logger';

export interface SecurityEvent {
  event: string;
  userId?: string;
  factoryId?: string;
  auth0Id?: string;
  ip: string;
  userAgent?: string;
  error?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface BusinessEvent {
  event: string;
  entityType: string;
  entityId?: string;
  userId: string;
  factoryId?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export class AuditLogger {
  // Log security-related events
  static async logSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      // Log to security log file
      securityLogger.info('Security event', event);
      
      // Store in database for compliance
      await prisma.auditLog.create({
        data: {
          type: 'SECURITY',
          event: event.event,
          userId: event.userId,
          factoryId: event.factoryId,
          ipAddress: event.ip,
          userAgent: event.userAgent,
          metadata: {
            auth0Id: event.auth0Id,
            error: event.error,
            ...event.metadata,
          },
          timestamp: event.timestamp,
        },
      });
      
      // Send critical security events to monitoring
      if (this.isCriticalSecurityEvent(event.event)) {
        await this.sendToMonitoring('security', event);
      }
    } catch (error) {
      logger.error('Failed to log security event', {
        error: error.message,
        originalEvent: event,
      });
    }
  }
  
  // Log business-related events
  static async logBusinessEvent(event: BusinessEvent): Promise<void> {
    try {
      // Log to main log file
      logger.info('Business event', event);
      
      // Store in database
      await prisma.auditLog.create({
        data: {
          type: 'BUSINESS',
          event: event.event,
          entityType: event.entityType,
          entityId: event.entityId,
          userId: event.userId,
          factoryId: event.factoryId,
          metadata: event.metadata || {},
          timestamp: event.timestamp,
        },
      });
    } catch (error) {
      logger.error('Failed to log business event', {
        error: error.message,
        originalEvent: event,
      });
    }
  }
  
  // Log system events (performance, errors, etc.)
  static async logSystemEvent(event: {
    event: string;
    level: 'info' | 'warn' | 'error';
    component: string;
    metadata?: Record<string, any>;
    timestamp: Date;
  }): Promise<void> {
    try {
      logger[event.level]('System event', {
        event: event.event,
        component: event.component,
        ...event.metadata,
        timestamp: event.timestamp,
      });
      
      // Store critical system events
      if (event.level === 'error') {
        await prisma.auditLog.create({
          data: {
            type: 'SYSTEM',
            event: event.event,
            metadata: {
              level: event.level,
              component: event.component,
              ...event.metadata,
            },
            timestamp: event.timestamp,
          },
        });
      }
    } catch (error) {
      console.error('Failed to log system event:', error);
    }
  }
  
  private static isCriticalSecurityEvent(event: string): boolean {
    const criticalEvents = [
      'AUTH_FAILURE',
      'PERMISSION_DENIED',
      'SUSPICIOUS_ACTIVITY',
      'RATE_LIMIT_EXCEEDED',
      'SQL_INJECTION_ATTEMPT',
      'XSS_ATTEMPT',
    ];
    
    return criticalEvents.includes(event);
  }
  
  private static async sendToMonitoring(type: string, event: any): Promise<void> {
    // Send to external monitoring service (DataDog, Sentry, etc.)
    if (config.DATADOG_API_KEY) {
      // Implementation for DataDog
    }
    
    if (config.SENTRY_DSN) {
      // Implementation for Sentry
    }
  }
}
```

### **Health Check & Metrics**

#### **Comprehensive Health Checks**
```typescript
// src/routers/health.ts
import { Router } from 'express';
import { checkDatabaseHealth } from '../lib/database/connection';
import { checkRedisHealth } from '../lib/redis';
import { config } from '../lib/config';
import { logger } from '../lib/logging/logger';

const router = Router();

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  uptime: number;
  checks: {
    database: {
      status: 'healthy' | 'unhealthy';
      latency: number;
      activeConnections?: number;
    };
    redis: {
      status: 'healthy' | 'unhealthy';
      latency: number;
    };
    memory: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      usage: number;
      limit: number;
      percentage: number;
    };
    disk: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      usage: number;
      available: number;
      percentage: number;
    };
  };
}

// Basic health check
router.get('/', async (req, res) => {
  try {
    const health: HealthStatus = await performHealthChecks();
    
    const statusCode = health.status === 'healthy' ? 200 
      : health.status === 'degraded' ? 200 
      : 503;
    
    res.status(statusCode).json(health);
  } catch (error) {
    logger.error('Health check failed', { error: error.message });
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
});

// Detailed health check (protected)
router.get('/detailed', async (req, res) => {
  // Verify health check token
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (token !== config.HEALTH_CHECK_TOKEN) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  try {
    const health = await performDetailedHealthChecks();
    res.json(health);
  } catch (error) {
    logger.error('Detailed health check failed', { error: error.message });
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
    });
  }
});

async function performHealthChecks(): Promise<HealthStatus> {
  const startTime = Date.now();
  
  // Check database
  const dbHealth = await checkDatabaseHealth();
  
  // Check Redis
  const redisHealth = await checkRedisHealth();
  
  // Check memory usage
  const memoryUsage = process.memoryUsage();
  const memoryLimit = 1024 * 1024 * 1024; // 1GB limit
  const memoryPercentage = (memoryUsage.heapUsed / memoryLimit) * 100;
  
  // Check disk usage (simplified)
  const diskUsage = await getDiskUsage();
  
  const checks = {
    database: {
      status: dbHealth.healthy ? 'healthy' as const : 'unhealthy' as const,
      latency: dbHealth.latency,
      activeConnections: dbHealth.activeConnections,
    },
    redis: {
      status: redisHealth ? 'healthy' as const : 'unhealthy' as const,
      latency: Date.now() - startTime,
    },
    memory: {
      status: memoryPercentage < 80 ? 'healthy' as const 
        : memoryPercentage < 95 ? 'degraded' as const 
        : 'unhealthy' as const,
      usage: memoryUsage.heapUsed,
      limit: memoryLimit,
      percentage: memoryPercentage,
    },
    disk: {
      status: diskUsage.percentage < 80 ? 'healthy' as const
        : diskUsage.percentage < 95 ? 'degraded' as const
        : 'unhealthy' as const,
      usage: diskUsage.used,
      available: diskUsage.available,
      percentage: diskUsage.percentage,
    },
  };
  
  // Determine overall status
  const hasUnhealthy = Object.values(checks).some(check => check.status === 'unhealthy');
  const hasDegraded = Object.values(checks).some(check => check.status === 'degraded');
  
  const status = hasUnhealthy ? 'unhealthy' 
    : hasDegraded ? 'degraded' 
    : 'healthy';
  
  return {
    status,
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: config.NODE_ENV,
    uptime: process.uptime(),
    checks,
  };
}

async function performDetailedHealthChecks() {
  const basic = await performHealthChecks();
  
  // Add more detailed checks
  return {
    ...basic,
    detailed: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid,
      cpuUsage: process.cpuUsage(),
      resourceUsage: process.resourceUsage(),
      env: {
        nodeEnv: config.NODE_ENV,
        port: config.PORT,
        databaseConnected: basic.checks.database.status === 'healthy',
        redisConnected: basic.checks.redis.status === 'healthy',
      },
    },
  };
}

async function getDiskUsage(): Promise<{
  used: number;
  available: number;
  percentage: number;
}> {
  // Simplified disk usage check
  // In production, use a proper disk usage library
  return {
    used: 0,
    available: **********, // 1GB
    percentage: 0,
  };
}

export { router as healthCheckRouter };
```

#### **Application Metrics**
```typescript
// src/routers/metrics.ts
import { Router } from 'express';
import { register, collectDefaultMetrics, Counter, Histogram, Gauge } from 'prom-client';
import { config } from '../lib/config';
import { prisma } from '../lib/database/connection';

const router = Router();

// Collect default metrics
collectDefaultMetrics();

// Custom metrics
export const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
});

export const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route'],
  buckets: [0.1, 0.5, 1, 2, 5],
});

export const databaseQueryDuration = new Histogram({
  name: 'database_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['operation', 'table'],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2],
});

export const activeUsers = new Gauge({
  name: 'active_users_total',
  help: 'Number of active users',
  labelNames: ['type'],
});

export const businessMetrics = {
  totalFactories: new Gauge({
    name: 'total_factories',
    help: 'Total number of factories',
  }),
  totalProducts: new Gauge({
    name: 'total_products',
    help: 'Total number of products',
  }),
  totalOrders: new Gauge({
    name: 'total_orders',
    help: 'Total number of orders',
  }),
};

// Metrics endpoint
router.get('/', async (req, res) => {
  // Verify metrics token
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (token !== config.HEALTH_CHECK_TOKEN) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  try {
    // Update business metrics
    await updateBusinessMetrics();
    
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    res.status(500).json({ error: 'Failed to collect metrics' });
  }
});

async function updateBusinessMetrics() {
  try {
    const [factoryCount, productCount, orderCount, activeUserCount] = await Promise.all([
      prisma.factory.count({ where: { status: 'ACTIVE' } }),
      prisma.product.count({ where: { status: 'ACTIVE' } }),
      prisma.order.count(),
      prisma.user.count({ where: { status: 'ACTIVE' } }),
    ]);
    
    businessMetrics.totalFactories.set(factoryCount);
    businessMetrics.totalProducts.set(productCount);
    businessMetrics.totalOrders.set(orderCount);
    activeUsers.set({ type: 'total' }, activeUserCount);
  } catch (error) {
    console.error('Failed to update business metrics:', error);
  }
}

export { router as metricsRouter };
```
