# FC-CHINA Supabase Setup Guide

## 🎯 **Your Supabase Project Configuration**

### **Project Details**
- **Project Name**: fc-china
- **Project URL**: https://ejrxrhojmrjpjodogtxq.supabase.co
- **Project ID**: ejrxrhojmrjpjodogtxq
- **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqcnhyaG9qbXJqcGpvZG9ndHhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDcwNTcsImV4cCI6MjA2ODU4MzA1N30.mqyGNK_0N0oawTGAyNQxAoH7nW8jrLRoSKmOTmySXAc`

---

## 🔧 **Required Supabase Configuration Steps**

### **Step 1: Get Service Role Key**
1. Go to your Supabase Dashboard: https://supabase.com/dashboard/project/ejrxrhojmrjpjodogtxq
2. Navigate to **Settings** → **API**
3. Copy the **service_role** key (not the anon key)
4. Add it to your `.env.local` file:
   ```bash
   SUPABASE_SERVICE_KEY="your-service-role-key-here"
   ```

### **Step 2: Database Connection String**
1. In Supabase Dashboard, go to **Settings** → **Database**
2. Copy the **Connection string** under "Connection parameters"
3. Replace the placeholder password with your actual database password
4. Add to `.env.local`:
   ```bash
   DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.ejrxrhojmrjpjodogtxq.supabase.co:5432/postgres"
   ```

### **Step 3: Configure Storage Bucket**
1. Go to **Storage** in your Supabase Dashboard
2. Create a new bucket named `fc-china-uploads`
3. Set bucket to **Public** for file access
4. Configure the following policies:

#### **Storage Policies**
```sql
-- Allow authenticated users to upload files
CREATE POLICY "Authenticated users can upload files" ON storage.objects
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Allow public read access to files
CREATE POLICY "Public read access" ON storage.objects
FOR SELECT USING (bucket_id = 'fc-china-uploads');

-- Allow users to update their own files
CREATE POLICY "Users can update own files" ON storage.objects
FOR UPDATE USING (auth.uid()::text = (storage.foldername(name))[1]);

-- Allow users to delete their own files
CREATE POLICY "Users can delete own files" ON storage.objects
FOR DELETE USING (auth.uid()::text = (storage.foldername(name))[1]);
```

### **Step 4: Enable Row Level Security**
1. Go to **Authentication** → **Policies**
2. Enable RLS on all tables (will be created by Prisma migrations)
3. We'll add specific policies after running migrations

### **Step 5: Configure Authentication**
1. Go to **Authentication** → **Settings**
2. Configure **Site URL**: `http://localhost:3000` (for development)
3. Add **Redirect URLs**:
   - `http://localhost:3000/auth/callback`
   - `http://localhost:3000/auth/confirm`
4. Enable **Email confirmations** if desired

---

## 🔄 **Integration with Prisma**

### **Database Configuration**
Your Supabase PostgreSQL database will be used as the primary database for Prisma. The connection string format is:

```bash
# Development
DATABASE_URL="postgresql://postgres:[PASSWORD]@db.ejrxrhojmrjpjodogtxq.supabase.co:5432/postgres"

# For connection pooling (recommended for production)
DIRECT_URL="postgresql://postgres:[PASSWORD]@db.ejrxrhojmrjpjodogtxq.supabase.co:5432/postgres"
```

### **Prisma Schema Configuration**
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}
```

---

## 🚀 **Development Environment Setup**

### **Updated .env.local Configuration**
```bash
# Copy this to your .env.local file and fill in the missing values

# =============================================================================
# DATABASE CONFIGURATION (Supabase)
# =============================================================================
DATABASE_URL="postgresql://postgres:[YOUR-DB-PASSWORD]@db.ejrxrhojmrjpjodogtxq.supabase.co:5432/postgres"
DIRECT_URL="postgresql://postgres:[YOUR-DB-PASSWORD]@db.ejrxrhojmrjpjodogtxq.supabase.co:5432/postgres"

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
SUPABASE_URL="https://ejrxrhojmrjpjodogtxq.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqcnhyaG9qbXJqcGpvZG9ndHhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDcwNTcsImV4cCI6MjA2ODU4MzA1N30.mqyGNK_0N0oawTGAyNQxAoH7nW8jrLRoSKmOTmySXAc"
SUPABASE_SERVICE_KEY="[GET-FROM-SUPABASE-DASHBOARD]"

# =============================================================================
# AUTH0 CONFIGURATION (You'll need to set this up)
# =============================================================================
AUTH0_DOMAIN="your-domain.auth0.com"
AUTH0_CLIENT_ID="your-client-id"
AUTH0_CLIENT_SECRET="your-client-secret"
AUTH0_AUDIENCE="https://api.fc-china.com"

# =============================================================================
# NEXTJS CONFIGURATION
# =============================================================================
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-32-chars-minimum"

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_URL="http://localhost:3001"
NEXT_PUBLIC_API_URL="http://localhost:3001"
NEXT_PUBLIC_SUPABASE_URL="https://ejrxrhojmrjpjodogtxq.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVqcnhyaG9qbXJqcGpvZG9ndHhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMDcwNTcsImV4cCI6MjA2ODU4MzA1N30.mqyGNK_0N0oawTGAyNQxAoH7nW8jrLRoSKmOTmySXAc"

# =============================================================================
# REDIS CONFIGURATION (Local for development)
# =============================================================================
REDIS_URL="redis://localhost:6379"

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
NODE_ENV="development"
LOG_LEVEL="debug"
ENABLE_QUERY_LOGGING="true"
CORS_ORIGIN="http://localhost:3000,http://localhost:3001"
```

---

## 🔄 **Updated Docker Compose (Hybrid Setup)**

Since you're using Supabase for the database, we'll update the Docker Compose to only include local services:

```yaml
# docker-compose.yml - Updated for Supabase integration
version: '3.8'

services:
  # Redis Cache (Local)
  redis:
    image: redis:7-alpine
    container_name: fc-china-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - fc-china-network

  # API Backend
  api:
    build:
      context: ./apps/api
      dockerfile: Dockerfile.dev
    container_name: fc-china-api
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - REDIS_URL=redis://redis:6379
      - AUTH0_DOMAIN=${AUTH0_DOMAIN}
      - AUTH0_AUDIENCE=${AUTH0_AUDIENCE}
    ports:
      - "3001:3001"
    volumes:
      - ./apps/api:/app
      - /app/node_modules
    depends_on:
      - redis
    networks:
      - fc-china-network
    command: npm run dev

  # Web Frontend
  web:
    build:
      context: ./apps/web
      dockerfile: Dockerfile.dev
    container_name: fc-china-web
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:3001
      - NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
    ports:
      - "3000:3000"
    volumes:
      - ./apps/web:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - api
    networks:
      - fc-china-network
    command: npm run dev

  # Mailhog (Email Testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: fc-china-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - fc-china-network

volumes:
  redis_data:
    driver: local

networks:
  fc-china-network:
    driver: bridge
```

---

## ✅ **Quick Start Checklist**

### **Before Starting Development**
- [ ] Get service_role key from Supabase Dashboard
- [ ] Get database password from Supabase Dashboard
- [ ] Create storage bucket `fc-china-uploads`
- [ ] Configure storage policies
- [ ] Set up Auth0 account (optional for initial development)
- [ ] Update `.env.local` with all values

### **Start Development**
```bash
# 1. Clone and setup
git clone <your-repo>
cd fc-china
npm install

# 2. Configure environment
cp .env.example .env.local
# Edit .env.local with your Supabase credentials

# 3. Run database migrations
npm run db:generate
npm run db:push

# 4. Start development environment
docker-compose up -d
npm run dev:all

# 5. Verify setup
curl http://localhost:3001/health
curl http://localhost:3000
```

---

## 🎯 **Next Steps**

1. **Complete Supabase Setup** (30 minutes)
2. **Set up Auth0** (optional, can use Supabase Auth initially)
3. **Start Phase 1 Development** following the implementation plan
4. **Deploy to Staging** using Supabase production database

**🚀 You're ready to start development with your Supabase project!**
