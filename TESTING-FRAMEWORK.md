# FC-CHINA Testing Framework

## 📋 **Testing Strategy Overview**

### **Testing Pyramid**
```
                    /\
                   /  \
                  / E2E \
                 /______\
                /        \
               /Integration\
              /__________\
             /            \
            /     Unit     \
           /________________\
```

- **Unit Tests (70%)**: Component and function testing
- **Integration Tests (20%)**: API and database testing  
- **E2E Tests (10%)**: Full user journey testing

### **Testing Tools Stack**
- **Unit Testing**: Jest + React Testing Library + Flutter Test
- **Integration Testing**: Jest + Supertest + Prisma Test Environment
- **E2E Testing**: Playwright + Cypress (mobile)
- **Visual Testing**: Chromatic + Storybook
- **Performance Testing**: Lighthouse CI + K6

---

## 🧪 **Unit Testing Configuration**

### **Jest Configuration**
```javascript
// jest.config.js
const { createJestConfig } = require('./jest.config.base');

module.exports = createJestConfig({
  displayName: 'FC-CHINA',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testMatch: [
    '<rootDir>/apps/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/apps/**/*.{test,spec}.{js,jsx,ts,tsx}',
    '<rootDir>/packages/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/packages/**/*.{test,spec}.{js,jsx,ts,tsx}'
  ],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/apps/web/src/$1',
    '^@api/(.*)$': '<rootDir>/apps/api/src/$1',
    '^@shared/(.*)$': '<rootDir>/packages/shared-types/src/$1'
  },
  collectCoverageFrom: [
    'apps/**/*.{js,jsx,ts,tsx}',
    'packages/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/.next/**',
    '!**/dist/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
});
```

### **Jest Setup File**
```javascript
// jest.setup.js
import '@testing-library/jest-dom';
import { server } from './src/mocks/server';
import { TextEncoder, TextDecoder } from 'util';

// Polyfills
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/'
  })
}));

// Mock Auth0
jest.mock('@auth0/nextjs-auth0', () => ({
  useUser: () => ({
    user: {
      sub: 'auth0|123',
      email: '<EMAIL>',
      name: 'Test User'
    },
    isLoading: false
  }),
  withApiAuthRequired: (handler) => handler,
  withPageAuthRequired: (component) => component
}));

// Setup MSW
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));
```

### **React Component Testing**
```typescript
// apps/web/src/components/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '../Button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('shows loading state correctly', () => {
    render(<Button loading>Click me</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('applies variant styles correctly', () => {
    render(<Button variant="secondary">Secondary</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-secondary-600');
  });

  it('meets accessibility requirements', async () => {
    const { container } = render(<Button>Accessible Button</Button>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

### **tRPC API Testing**
```typescript
// apps/api/src/routers/__tests__/products.test.ts
import { createTRPCMsw } from 'msw-trpc';
import { appRouter } from '../index';
import { createContext } from '../../lib/context';

const trpcMsw = createTRPCMsw(appRouter);

describe('Products Router', () => {
  beforeEach(() => {
    // Reset database state
    jest.clearAllMocks();
  });

  it('creates product successfully', async () => {
    const ctx = await createContext({
      user: { id: 'user-1', factoryId: 'factory-1', role: 'FACTORY_ADMIN' }
    });

    const input = {
      name: 'Test Product',
      description: 'Test Description',
      categoryId: 'category-1',
      basePrice: 99.99,
      stockQuantity: 100
    };

    const result = await appRouter
      .createCaller(ctx)
      .products.createProduct(input);

    expect(result).toMatchObject({
      name: 'Test Product',
      basePrice: 99.99,
      factoryId: 'factory-1'
    });
  });

  it('validates input correctly', async () => {
    const ctx = await createContext({
      user: { id: 'user-1', factoryId: 'factory-1', role: 'FACTORY_ADMIN' }
    });

    await expect(
      appRouter.createCaller(ctx).products.createProduct({
        name: '', // Invalid empty name
        categoryId: 'category-1',
        basePrice: -10 // Invalid negative price
      })
    ).rejects.toThrow();
  });

  it('enforces factory access control', async () => {
    const ctx = await createContext({
      user: { id: 'user-1', factoryId: 'factory-1', role: 'FACTORY_ADMIN' }
    });

    await expect(
      appRouter.createCaller(ctx).products.updateProduct({
        id: 'product-from-different-factory',
        name: 'Updated Name'
      })
    ).rejects.toThrow('Product not found');
  });
});
```

---

## 🔗 **Integration Testing**

### **API Integration Tests**
```typescript
// apps/api/src/__tests__/integration/products.integration.test.ts
import request from 'supertest';
import { app } from '../../app';
import { prisma } from '../../lib/prisma';
import { createTestUser, createTestFactory } from '../../test-utils';

describe('Products API Integration', () => {
  let factory: any;
  let user: any;
  let authToken: string;

  beforeAll(async () => {
    factory = await createTestFactory();
    user = await createTestUser({ factoryId: factory.id });
    authToken = generateTestToken(user);
  });

  afterAll(async () => {
    await prisma.product.deleteMany();
    await prisma.user.deleteMany();
    await prisma.factory.deleteMany();
  });

  it('creates product via API', async () => {
    const productData = {
      name: 'Integration Test Product',
      description: 'Test Description',
      categoryId: factory.categories[0].id,
      basePrice: 149.99,
      stockQuantity: 50
    };

    const response = await request(app)
      .post('/api/trpc/products.createProduct')
      .set('Authorization', `Bearer ${authToken}`)
      .send({ input: productData })
      .expect(200);

    expect(response.body.result.data).toMatchObject({
      name: productData.name,
      basePrice: productData.basePrice,
      factoryId: factory.id
    });

    // Verify in database
    const dbProduct = await prisma.product.findFirst({
      where: { name: productData.name }
    });
    expect(dbProduct).toBeTruthy();
  });

  it('handles file upload integration', async () => {
    const response = await request(app)
      .post('/api/trpc/uploads.generateUploadUrl')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        input: {
          fileName: 'test-image.jpg',
          fileType: 'image/jpeg',
          fileSize: 1024000,
          uploadType: 'product-image'
        }
      })
      .expect(200);

    expect(response.body.result.data).toHaveProperty('uploadUrl');
    expect(response.body.result.data).toHaveProperty('publicUrl');
  });
});
```

### **Database Integration Tests**
```typescript
// apps/api/src/__tests__/integration/database.integration.test.ts
import { prisma } from '../../lib/prisma';
import { createTestFactory, createTestUser, createTestProduct } from '../../test-utils';

describe('Database Integration', () => {
  it('maintains referential integrity', async () => {
    const factory = await createTestFactory();
    const user = await createTestUser({ factoryId: factory.id });
    const product = await createTestProduct({ factoryId: factory.id });

    // Test cascade delete
    await prisma.factory.delete({ where: { id: factory.id } });

    // Verify related records are deleted
    const deletedUser = await prisma.user.findUnique({ where: { id: user.id } });
    const deletedProduct = await prisma.product.findUnique({ where: { id: product.id } });

    expect(deletedUser).toBeNull();
    expect(deletedProduct).toBeNull();
  });

  it('enforces unique constraints', async () => {
    const factory = await createTestFactory();
    
    await expect(
      prisma.factory.create({
        data: {
          name: 'Duplicate Factory',
          slug: factory.slug, // Duplicate slug
          email: '<EMAIL>',
          industry: 'Electronics'
        }
      })
    ).rejects.toThrow();
  });

  it('handles concurrent operations correctly', async () => {
    const factory = await createTestFactory();
    const product = await createTestProduct({ 
      factoryId: factory.id,
      stockQuantity: 100
    });

    // Simulate concurrent stock updates
    const updates = Array.from({ length: 10 }, (_, i) =>
      prisma.product.update({
        where: { id: product.id },
        data: { stockQuantity: { decrement: 1 } }
      })
    );

    await Promise.all(updates);

    const updatedProduct = await prisma.product.findUnique({
      where: { id: product.id }
    });

    expect(updatedProduct?.stockQuantity).toBe(90);
  });
});
```

---

## 🎭 **End-to-End Testing**

### **Playwright Configuration**
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['junit', { outputFile: 'test-results/junit.xml' }]
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] }
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] }
    }
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI
  }
});
```

### **E2E Test Examples**
```typescript
// e2e/factory-dashboard.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Factory Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Login as factory admin
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('/dashboard');
  });

  test('displays key metrics correctly', async ({ page }) => {
    await expect(page.locator('[data-testid="total-products"]')).toBeVisible();
    await expect(page.locator('[data-testid="active-orders"]')).toBeVisible();
    await expect(page.locator('[data-testid="monthly-revenue"]')).toBeVisible();

    // Check that metrics have actual values
    const totalProducts = await page.locator('[data-testid="total-products"]').textContent();
    expect(totalProducts).toMatch(/\d+/);
  });

  test('navigates to product management', async ({ page }) => {
    await page.click('[data-testid="products-nav-link"]');
    await page.waitForURL('/products');

    await expect(page.locator('h1')).toContainText('Products');
    await expect(page.locator('[data-testid="add-product-button"]')).toBeVisible();
  });

  test('creates new product successfully', async ({ page }) => {
    await page.goto('/products');
    await page.click('[data-testid="add-product-button"]');

    // Fill product form
    await page.fill('[data-testid="product-name"]', 'E2E Test Product');
    await page.fill('[data-testid="product-description"]', 'Created by E2E test');
    await page.selectOption('[data-testid="product-category"]', 'electronics');
    await page.fill('[data-testid="product-price"]', '99.99');
    await page.fill('[data-testid="product-stock"]', '100');

    // Submit form
    await page.click('[data-testid="save-product-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="product-list"]')).toContainText('E2E Test Product');
  });

  test('handles real-time order updates', async ({ page, context }) => {
    // Open dashboard in first tab
    await page.goto('/dashboard');

    // Open second tab to simulate customer order
    const customerPage = await context.newPage();
    await customerPage.goto('/marketplace');

    // Place order as customer (simplified)
    await customerPage.click('[data-testid="first-product"]');
    await customerPage.click('[data-testid="contact-factory"]');
    await customerPage.fill('[data-testid="message-input"]', 'I want to order this product');
    await customerPage.click('[data-testid="send-message"]');

    // Check that factory dashboard shows new message notification
    await expect(page.locator('[data-testid="message-notification"]')).toBeVisible();
  });
});
```

### **Flutter Testing Configuration**
```dart
// apps/mobile/test/widget_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:fc_china_mobile/main.dart';
import 'package:fc_china_mobile/services/api_service.dart';

// Generate mocks
@GenerateMocks([ApiService])
void main() {
  group('Product List Widget Tests', () => {
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
    });

    testWidgets('displays loading state initially', (WidgetTester tester) async {
      when(mockApiService.getProducts(any))
          .thenAnswer((_) async => Future.delayed(Duration(seconds: 1), () => []));

      await tester.pumpWidget(MyApp(apiService: mockApiService));
      await tester.pumpAndSettle();

      // Navigate to products
      await tester.tap(find.byIcon(Icons.inventory_2));
      await tester.pump();

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('displays products when loaded', (WidgetTester tester) async {
      final mockProducts = [
        Product(id: '1', name: 'Test Product 1', price: 99.99),
        Product(id: '2', name: 'Test Product 2', price: 149.99),
      ];

      when(mockApiService.getProducts(any))
          .thenAnswer((_) async => mockProducts);

      await tester.pumpWidget(MyApp(apiService: mockApiService));
      await tester.pumpAndSettle();

      // Navigate to products
      await tester.tap(find.byIcon(Icons.inventory_2));
      await tester.pumpAndSettle();

      expect(find.text('Test Product 1'), findsOneWidget);
      expect(find.text('Test Product 2'), findsOneWidget);
      expect(find.text('\$99.99'), findsOneWidget);
    });

    testWidgets('handles pull to refresh', (WidgetTester tester) async {
      when(mockApiService.getProducts(any))
          .thenAnswer((_) async => []);

      await tester.pumpWidget(MyApp(apiService: mockApiService));
      await tester.pumpAndSettle();

      // Navigate to products
      await tester.tap(find.byIcon(Icons.inventory_2));
      await tester.pumpAndSettle();

      // Pull to refresh
      await tester.fling(find.byType(ListView), Offset(0, 300), 1000);
      await tester.pump();

      // Verify refresh indicator appears
      expect(find.byType(RefreshIndicator), findsOneWidget);

      // Verify API is called again
      verify(mockApiService.getProducts(any)).called(2);
    });
  });

  group('Integration Tests', () => {
    testWidgets('complete user flow - browse to product details', (WidgetTester tester) async {
      // Setup mock responses
      when(mockApiService.getProducts(any))
          .thenAnswer((_) async => [Product(id: '1', name: 'Test Product', price: 99.99)]);
      when(mockApiService.getProduct('1'))
          .thenAnswer((_) async => ProductDetails(id: '1', name: 'Test Product', description: 'Test Description'));

      await tester.pumpWidget(MyApp(apiService: mockApiService));
      await tester.pumpAndSettle();

      // Navigate to products
      await tester.tap(find.byIcon(Icons.inventory_2));
      await tester.pumpAndSettle();

      // Tap on product
      await tester.tap(find.text('Test Product'));
      await tester.pumpAndSettle();

      // Verify product details page
      expect(find.text('Test Description'), findsOneWidget);
      expect(find.byType(FloatingActionButton), findsOneWidget); // Contact button
    });
  });
}
```

---

## 📊 **Test Reporting & CI Integration**

### **GitHub Actions Workflow**
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run database migrations
        run: npm run db:migrate
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test

      - name: Run integration tests
        run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright
        run: npx playwright install --with-deps

      - name: Start application
        run: npm run dev &

      - name: Wait for application
        run: npx wait-on http://localhost:3000

      - name: Run E2E tests
        run: npm run test:e2e

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/

  mobile-tests:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'

      - name: Install dependencies
        run: flutter pub get
        working-directory: apps/mobile

      - name: Run Flutter tests
        run: flutter test
        working-directory: apps/mobile

      - name: Run Flutter integration tests
        run: flutter drive --target=test_driver/app.dart
        working-directory: apps/mobile
```

### **Test Coverage Requirements**
- **Minimum Coverage**: 80% for all packages
- **Critical Paths**: 95% coverage required
- **New Code**: 90% coverage required
- **Coverage Reports**: Generated on every PR

### **Performance Testing**
```javascript
// k6-performance-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200 users
    { duration: '5m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.1'],    // Error rate under 10%
  },
};

export default function () {
  // Test API endpoints
  let response = http.get('http://localhost:3001/api/health');
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 100ms': (r) => r.timings.duration < 100,
  });

  // Test product listing
  response = http.get('http://localhost:3001/api/trpc/products.getProducts', {
    headers: { 'Authorization': 'Bearer test-token' }
  });
  check(response, {
    'products loaded': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });

  sleep(1);
}
```
```
