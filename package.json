{"name": "fc-china", "version": "1.0.0", "description": "FC-CHINA Multi-Tenant B2B Manufacturing Platform", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev:all": "turbo run dev --parallel", "dev:api": "turbo run dev --filter=api", "dev:web": "turbo run dev --filter=web", "dev:mobile": "cd apps/mobile && flutter run", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "type-check": "turbo run type-check", "test": "turbo run test", "test:api": "turbo run test --filter=api", "test:web": "turbo run test --filter=web", "test:mobile": "cd apps/mobile && flutter test", "test:e2e": "turbo run test:e2e", "clean": "turbo run clean && rm -rf node_modules", "db:generate": "turbo run db:generate", "db:push": "turbo run db:push", "db:migrate": "turbo run db:migrate", "db:studio": "turbo run db:studio", "db:seed": "turbo run db:seed", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "validate:env": "turbo run validate:env", "health-check": "curl -f http://localhost:3001/health && curl -f http://localhost:3000", "install:all": "npm install && cd apps/mobile && flutter pub get"}, "devDependencies": {"@turbo/gen": "^1.11.2", "turbo": "^1.11.2", "prettier": "^3.1.1", "eslint": "^8.56.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "packageManager": "npm@10.2.4"}